import { BaseEntity, LocalizedString, FileUpload } from './index';

export interface ChatConversation extends BaseEntity {
  id: string;
  participants: ChatParticipant[];
  type: ConversationType;
  bookingId?: string;
  title?: LocalizedString;
  lastMessage?: ChatMessage;
  unreadCount: Record<string, number>; // userId -> unread count
  status: ConversationStatus;
  metadata: ConversationMetadata;
}

export interface ChatParticipant {
  userId: string;
  role: ParticipantRole;
  joinedAt: Date;
  leftAt?: Date;
  permissions: ParticipantPermission[];
  lastReadAt?: Date;
  muted: boolean;
}

export type ConversationType = 
  | 'direct' // 1-on-1 between client and expert
  | 'booking' // Related to a specific booking
  | 'support' // Customer support conversation
  | 'group'; // Group conversation (future feature)

export type ConversationStatus = 
  | 'active'
  | 'archived'
  | 'blocked'
  | 'deleted';

export type ParticipantRole = 
  | 'client'
  | 'expert'
  | 'admin'
  | 'support';

export type ParticipantPermission = 
  | 'send_message'
  | 'send_file'
  | 'delete_message'
  | 'archive_conversation'
  | 'add_participant'
  | 'remove_participant';

export interface ConversationMetadata {
  createdBy: string;
  source: ConversationSource;
  tags?: string[];
  priority?: ConversationPriority;
  language: 'ar' | 'en';
  autoTranslate: boolean;
}

export type ConversationSource = 
  | 'booking_request'
  | 'service_inquiry'
  | 'support_request'
  | 'direct_message'
  | 'system_generated';

export type ConversationPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface ChatMessage extends BaseEntity {
  id: string;
  conversationId: string;
  senderId: string;
  senderType: ParticipantRole;
  content: MessageContent;
  type: MessageType;
  status: MessageStatus;
  replyTo?: string; // Message ID being replied to
  reactions: MessageReaction[];
  editHistory: MessageEdit[];
  metadata: MessageMetadata;
}

export interface MessageContent {
  text?: LocalizedString;
  files?: FileUpload[];
  systemData?: SystemMessageData;
  templateData?: TemplateMessageData;
}

export type MessageType = 
  | 'text'
  | 'file'
  | 'image'
  | 'system'
  | 'template'
  | 'booking_update'
  | 'payment_update'
  | 'delivery_update';

export type MessageStatus = 
  | 'sending'
  | 'sent'
  | 'delivered'
  | 'read'
  | 'failed'
  | 'deleted';

export interface MessageReaction {
  userId: string;
  emoji: string;
  createdAt: Date;
}

export interface MessageEdit {
  editedAt: Date;
  previousContent: MessageContent;
  reason?: string;
}

export interface MessageMetadata {
  deviceInfo?: {
    type: 'mobile' | 'desktop' | 'web';
    os?: string;
    app?: string;
  };
  location?: {
    country: string;
    city: string;
  };
  translated?: boolean;
  originalLanguage?: string;
  moderationFlags?: ModerationFlag[];
}

export interface ModerationFlag {
  type: ModerationFlagType;
  confidence: number;
  details?: string;
  reviewedBy?: string;
  reviewedAt?: Date;
  action?: ModerationAction;
}

export type ModerationFlagType = 
  | 'spam'
  | 'inappropriate_content'
  | 'personal_info_sharing'
  | 'external_contact'
  | 'harassment'
  | 'fraud_attempt';

export type ModerationAction = 
  | 'none'
  | 'warning'
  | 'message_hidden'
  | 'user_warned'
  | 'user_suspended'
  | 'conversation_locked';

export interface SystemMessageData {
  type: SystemMessageType;
  data: Record<string, any>;
  actionRequired?: boolean;
  actionUrl?: string;
}

export type SystemMessageType = 
  | 'booking_created'
  | 'booking_accepted'
  | 'booking_rejected'
  | 'booking_completed'
  | 'payment_received'
  | 'delivery_submitted'
  | 'revision_requested'
  | 'dispute_opened'
  | 'user_joined'
  | 'user_left';

export interface TemplateMessageData {
  templateId: string;
  variables: Record<string, any>;
  language: 'ar' | 'en';
}

// Chat features
export interface TypingIndicator {
  conversationId: string;
  userId: string;
  isTyping: boolean;
  timestamp: Date;
}

export interface MessageDeliveryReceipt {
  messageId: string;
  userId: string;
  status: MessageStatus;
  timestamp: Date;
}

export interface ConversationSettings {
  conversationId: string;
  userId: string;
  notifications: NotificationSettings;
  autoTranslate: boolean;
  language: 'ar' | 'en';
  theme?: 'light' | 'dark';
}

export interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  preview: boolean;
  quietHours?: {
    start: string; // HH:mm
    end: string; // HH:mm
    timezone: string;
  };
}

// Chat search and filtering
export interface ChatSearchRequest {
  query?: string;
  conversationId?: string;
  senderId?: string;
  messageType?: MessageType;
  dateRange?: {
    start: Date;
    end: Date;
  };
  hasFiles?: boolean;
  language?: 'ar' | 'en';
}

export interface ChatSearchResult {
  messages: ChatMessage[];
  total: number;
  highlights: SearchHighlight[];
}

export interface SearchHighlight {
  messageId: string;
  field: string;
  fragments: string[];
}

// Chat analytics
export interface ChatAnalytics {
  period: AnalyticsPeriod;
  metrics: {
    totalConversations: number;
    totalMessages: number;
    averageResponseTime: number;
    activeUsers: number;
    messageTypes: Record<MessageType, number>;
    languageDistribution: Record<string, number>;
  };
  trends: ChatTrend[];
  userEngagement: UserEngagement[];
}

export type AnalyticsPeriod = '24h' | '7d' | '30d' | '90d';

export interface ChatTrend {
  date: string;
  conversations: number;
  messages: number;
  activeUsers: number;
  averageResponseTime: number;
}

export interface UserEngagement {
  userId: string;
  messagesSent: number;
  conversationsStarted: number;
  averageResponseTime: number;
  lastActive: Date;
}

// Real-time events
export interface ChatEvent {
  type: ChatEventType;
  conversationId: string;
  userId?: string;
  data: any;
  timestamp: Date;
}

export type ChatEventType = 
  | 'message_sent'
  | 'message_delivered'
  | 'message_read'
  | 'typing_start'
  | 'typing_stop'
  | 'user_joined'
  | 'user_left'
  | 'conversation_archived'
  | 'conversation_deleted';

// Chat moderation
export interface ChatModerationRule {
  id: string;
  name: string;
  description: string;
  type: ModerationRuleType;
  conditions: ModerationCondition[];
  actions: ModerationAction[];
  enabled: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export type ModerationRuleType = 
  | 'keyword_filter'
  | 'pattern_match'
  | 'ai_content_analysis'
  | 'rate_limiting'
  | 'user_behavior';

export interface ModerationCondition {
  field: string;
  operator: 'contains' | 'equals' | 'matches' | 'greater_than' | 'less_than';
  value: any;
  caseSensitive?: boolean;
}

// Chat templates
export interface ChatTemplate {
  id: string;
  name: LocalizedString;
  content: LocalizedString;
  category: TemplateCategory;
  variables: TemplateVariable[];
  usage: TemplateUsage;
  active: boolean;
}

export type TemplateCategory = 
  | 'greeting'
  | 'booking_inquiry'
  | 'delivery_notification'
  | 'revision_request'
  | 'completion_message'
  | 'support_response';

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  required: boolean;
  defaultValue?: any;
  description: LocalizedString;
}

export interface TemplateUsage {
  totalUsed: number;
  lastUsed?: Date;
  popularWith: ParticipantRole[];
}
