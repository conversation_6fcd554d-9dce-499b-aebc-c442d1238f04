import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation files
import arCommon from '../locales/ar/common.json';
import arAuth from '../locales/ar/auth.json';
import arExpert from '../locales/ar/expert.json';
import arClient from '../locales/ar/client.json';

import enCommon from '../locales/en/common.json';

// Translation resources
const resources = {
  ar: {
    common: arCommon,
    auth: arAuth,
    expert: arExpert,
    client: arClient,
  },
  en: {
    common: enCommon,
    auth: {},
    expert: {},
    client: {},
  },
};

// i18n configuration
export const i18nConfig = {
  resources,
  lng: 'ar', // Default language
  fallbackLng: 'ar', // Fallback language
  defaultNS: 'common',
  ns: ['common', 'auth', 'expert', 'client', 'booking', 'payment', 'chat', 'admin'],
  
  interpolation: {
    escapeValue: false, // React already escapes values
    formatSeparator: ',',
    format: (value: any, format: string | undefined, lng: string) => {
      if (format === 'uppercase') return value.toUpperCase();
      if (format === 'lowercase') return value.toLowerCase();
      if (format === 'currency') {
        return new Intl.NumberFormat(lng === 'ar' ? 'ar-SY' : 'en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(value);
      }
      if (format === 'date') {
        return new Intl.DateTimeFormat(lng === 'ar' ? 'ar-SY' : 'en-US').format(new Date(value));
      }
      return value;
    },
  },
  
  detection: {
    order: ['localStorage', 'navigator', 'htmlTag'],
    lookupLocalStorage: 'freela-language',
    caches: ['localStorage'],
  },
  
  react: {
    useSuspense: false,
    bindI18n: 'languageChanged',
    bindI18nStore: '',
    transEmptyNodeValue: '',
    transSupportBasicHtmlNodes: true,
    transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em'],
  },
  
  debug: process.env.NODE_ENV === 'development',
};

// Initialize i18n
i18n.use(initReactI18next).init(i18nConfig);

// Language detection for different environments
export const detectLanguage = (): string => {
  // Check localStorage first
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('freela-language');
    if (stored && ['ar', 'en'].includes(stored)) {
      return stored;
    }
  }
  
  // Check navigator language
  if (typeof navigator !== 'undefined') {
    const browserLang = navigator.language || (navigator as any).userLanguage;
    if (browserLang.startsWith('ar')) {
      return 'ar';
    }
  }
  
  // Default to Arabic
  return 'ar';
};

// Set language and persist
export const setLanguage = (language: string): void => {
  i18n.changeLanguage(language);
  
  if (typeof window !== 'undefined') {
    localStorage.setItem('freela-language', language);
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
  }
};

// Get current language
export const getCurrentLanguage = (): string => {
  return i18n.language || 'ar';
};

// Check if current language is RTL
export const isRTL = (language?: string): boolean => {
  const lang = language || getCurrentLanguage();
  return lang === 'ar';
};

// Get text direction
export const getDirection = (language?: string): 'ltr' | 'rtl' => {
  return isRTL(language) ? 'rtl' : 'ltr';
};

// Get text alignment
export const getTextAlign = (language?: string): 'left' | 'right' => {
  return isRTL(language) ? 'right' : 'left';
};

// Initialize language on app start
export const initializeLanguage = (): void => {
  const detectedLanguage = detectLanguage();
  setLanguage(detectedLanguage);
};

export default i18n;
