// URL utilities

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const isAbsoluteUrl = (url: string): boolean => {
  return /^https?:\/\//.test(url);
};

export const isRelativeUrl = (url: string): boolean => {
  return !isAbsoluteUrl(url) && !url.startsWith('//');
};

export const parseUrl = (url: string) => {
  try {
    const parsed = new URL(url);
    return {
      protocol: parsed.protocol,
      hostname: parsed.hostname,
      port: parsed.port,
      pathname: parsed.pathname,
      search: parsed.search,
      hash: parsed.hash,
      origin: parsed.origin,
      href: parsed.href,
    };
  } catch {
    return null;
  }
};

export const buildUrl = (
  base: string,
  path?: string,
  params?: Record<string, any>
): string => {
  let url = base;
  
  if (path) {
    url = url.replace(/\/$/, '') + '/' + path.replace(/^\//, '');
  }
  
  if (params && Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, String(item)));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });
    
    const queryString = searchParams.toString();
    if (queryString) {
      url += (url.includes('?') ? '&' : '?') + queryString;
    }
  }
  
  return url;
};

export const getQueryParams = (url: string): Record<string, string | string[]> => {
  try {
    const parsed = new URL(url);
    const params: Record<string, string | string[]> = {};
    
    for (const [key, value] of parsed.searchParams.entries()) {
      if (params[key]) {
        if (Array.isArray(params[key])) {
          (params[key] as string[]).push(value);
        } else {
          params[key] = [params[key] as string, value];
        }
      } else {
        params[key] = value;
      }
    }
    
    return params;
  } catch {
    return {};
  }
};

export const addQueryParams = (
  url: string,
  params: Record<string, any>
): string => {
  try {
    const parsed = new URL(url);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(item => parsed.searchParams.append(key, String(item)));
        } else {
          parsed.searchParams.set(key, String(value));
        }
      }
    });
    
    return parsed.toString();
  } catch {
    return url;
  }
};

export const removeQueryParams = (url: string, keys: string[]): string => {
  try {
    const parsed = new URL(url);
    
    keys.forEach(key => {
      parsed.searchParams.delete(key);
    });
    
    return parsed.toString();
  } catch {
    return url;
  }
};

export const updateQueryParams = (
  url: string,
  params: Record<string, any>
): string => {
  try {
    const parsed = new URL(url);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value === undefined || value === null) {
        parsed.searchParams.delete(key);
      } else if (Array.isArray(value)) {
        parsed.searchParams.delete(key);
        value.forEach(item => parsed.searchParams.append(key, String(item)));
      } else {
        parsed.searchParams.set(key, String(value));
      }
    });
    
    return parsed.toString();
  } catch {
    return url;
  }
};

export const getDomain = (url: string): string | null => {
  try {
    const parsed = new URL(url);
    return parsed.hostname;
  } catch {
    return null;
  }
};

export const getSubdomain = (url: string): string | null => {
  const domain = getDomain(url);
  if (!domain) return null;
  
  const parts = domain.split('.');
  if (parts.length <= 2) return null;
  
  return parts.slice(0, -2).join('.');
};

export const getRootDomain = (url: string): string | null => {
  const domain = getDomain(url);
  if (!domain) return null;
  
  const parts = domain.split('.');
  if (parts.length <= 1) return domain;
  
  return parts.slice(-2).join('.');
};

export const isSameDomain = (url1: string, url2: string): boolean => {
  const domain1 = getDomain(url1);
  const domain2 = getDomain(url2);
  
  return domain1 !== null && domain2 !== null && domain1 === domain2;
};

export const isSameOrigin = (url1: string, url2: string): boolean => {
  try {
    const parsed1 = new URL(url1);
    const parsed2 = new URL(url2);
    
    return parsed1.origin === parsed2.origin;
  } catch {
    return false;
  }
};

export const joinPaths = (...paths: string[]): string => {
  return paths
    .map((path, index) => {
      if (index === 0) {
        return path.replace(/\/$/, '');
      }
      return path.replace(/^\//, '').replace(/\/$/, '');
    })
    .filter(path => path.length > 0)
    .join('/');
};

export const normalizePath = (path: string): string => {
  return path
    .replace(/\/+/g, '/') // Replace multiple slashes with single slash
    .replace(/\/$/, '') // Remove trailing slash
    .replace(/^(?!\/)/, '/'); // Ensure leading slash
};

export const getPathSegments = (url: string): string[] => {
  try {
    const parsed = new URL(url);
    return parsed.pathname
      .split('/')
      .filter(segment => segment.length > 0);
  } catch {
    return url
      .replace(/^\//, '')
      .replace(/\/$/, '')
      .split('/')
      .filter(segment => segment.length > 0);
  }
};

export const getFileExtension = (url: string): string | null => {
  try {
    const parsed = new URL(url);
    const pathname = parsed.pathname;
    const lastDot = pathname.lastIndexOf('.');
    const lastSlash = pathname.lastIndexOf('/');
    
    if (lastDot > lastSlash && lastDot !== -1) {
      return pathname.substring(lastDot + 1).toLowerCase();
    }
    
    return null;
  } catch {
    const lastDot = url.lastIndexOf('.');
    const lastSlash = url.lastIndexOf('/');
    const lastQuestion = url.lastIndexOf('?');
    const lastHash = url.lastIndexOf('#');
    
    if (lastDot > lastSlash && lastDot !== -1) {
      let extension = url.substring(lastDot + 1);
      
      // Remove query parameters and hash
      if (lastQuestion !== -1 && lastQuestion > lastDot) {
        extension = extension.substring(0, lastQuestion - lastDot - 1);
      }
      if (lastHash !== -1 && lastHash > lastDot) {
        extension = extension.substring(0, lastHash - lastDot - 1);
      }
      
      return extension.toLowerCase();
    }
    
    return null;
  }
};

export const isImageUrl = (url: string): boolean => {
  const extension = getFileExtension(url);
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico'];
  return extension ? imageExtensions.includes(extension) : false;
};

export const isVideoUrl = (url: string): boolean => {
  const extension = getFileExtension(url);
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'];
  return extension ? videoExtensions.includes(extension) : false;
};

export const isAudioUrl = (url: string): boolean => {
  const extension = getFileExtension(url);
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'];
  return extension ? audioExtensions.includes(extension) : false;
};

export const isDocumentUrl = (url: string): boolean => {
  const extension = getFileExtension(url);
  const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'];
  return extension ? documentExtensions.includes(extension) : false;
};

export const encodeUrlComponent = (str: string): string => {
  return encodeURIComponent(str);
};

export const decodeUrlComponent = (str: string): string => {
  try {
    return decodeURIComponent(str);
  } catch {
    return str;
  }
};

export const sanitizeUrl = (url: string): string => {
  // Remove dangerous protocols
  const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:'];
  const lowerUrl = url.toLowerCase().trim();
  
  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      return 'about:blank';
    }
  }
  
  return url;
};

export const isSecureUrl = (url: string): boolean => {
  try {
    const parsed = new URL(url);
    return parsed.protocol === 'https:';
  } catch {
    return false;
  }
};

export const toSecureUrl = (url: string): string => {
  try {
    const parsed = new URL(url);
    if (parsed.protocol === 'http:') {
      parsed.protocol = 'https:';
    }
    return parsed.toString();
  } catch {
    return url;
  }
};

export const shortenUrl = (url: string, maxLength: number = 50): string => {
  if (url.length <= maxLength) return url;
  
  try {
    const parsed = new URL(url);
    const domain = parsed.hostname;
    const path = parsed.pathname;
    
    if (domain.length >= maxLength - 3) {
      return domain.substring(0, maxLength - 3) + '...';
    }
    
    const availableLength = maxLength - domain.length - 3; // 3 for "..."
    if (path.length > availableLength) {
      return domain + path.substring(0, availableLength) + '...';
    }
    
    return domain + path;
  } catch {
    return url.substring(0, maxLength - 3) + '...';
  }
};
