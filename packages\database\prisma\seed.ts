import { PrismaClient, UserRole, UserStatus, ServiceStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { nanoid } from 'nanoid';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create service categories
  console.log('📂 Creating service categories...');
  
  const categories = [
    {
      id: 'design',
      name: { ar: 'التصميم والجرافيك', en: 'Design & Graphics' },
      slug: 'design',
      description: { ar: 'خدمات التصميم الجرافيكي والإبداعي', en: 'Graphic and creative design services' },
      icon: '🎨',
      featured: true,
    },
    {
      id: 'programming',
      name: { ar: 'البرمجة والتطوير', en: 'Programming & Development' },
      slug: 'programming',
      description: { ar: 'خدمات البرمجة وتطوير المواقع والتطبيقات', en: 'Programming and web/app development services' },
      icon: '💻',
      featured: true,
    },
    {
      id: 'writing',
      name: { ar: 'الكتابة والترجمة', en: 'Writing & Translation' },
      slug: 'writing',
      description: { ar: 'خدمات الكتابة والترجمة والمحتوى', en: 'Writing, translation, and content services' },
      icon: '✍️',
      featured: true,
    },
    {
      id: 'marketing',
      name: { ar: 'التسويق الرقمي', en: 'Digital Marketing' },
      slug: 'marketing',
      description: { ar: 'خدمات التسويق الرقمي ووسائل التواصل الاجتماعي', en: 'Digital marketing and social media services' },
      icon: '📱',
      featured: true,
    },
    {
      id: 'video',
      name: { ar: 'الفيديو والصوت', en: 'Video & Audio' },
      slug: 'video',
      description: { ar: 'خدمات إنتاج الفيديو والصوت والمونتاج', en: 'Video and audio production and editing services' },
      icon: '🎬',
      featured: false,
    },
    {
      id: 'business',
      name: { ar: 'الأعمال والاستشارات', en: 'Business & Consulting' },
      slug: 'business',
      description: { ar: 'خدمات الاستشارات التجارية والإدارية', en: 'Business and management consulting services' },
      icon: '💼',
      featured: false,
    },
  ];

  for (const category of categories) {
    await prisma.serviceCategory.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    });
  }

  // Create admin user
  console.log('👤 Creating admin user...');
  
  const adminPassword = await bcrypt.hash('admin123!', 12);
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
      language: 'ar',
      emailVerified: true,
      passwordHash: adminPassword,
      location: {
        governorate: 'damascus',
        city: 'Damascus',
        district: 'Old City',
      },
    },
  });

  // Create sample expert users
  console.log('👨‍💻 Creating sample expert users...');
  
  const experts = [
    {
      email: '<EMAIL>',
      firstName: 'أحمد',
      lastName: 'المصمم',
      title: { ar: 'مصمم جرافيك محترف', en: 'Professional Graphic Designer' },
      description: { ar: 'مصمم جرافيك مع خبرة 5 سنوات في تصميم الهويات البصرية والمواقع', en: 'Graphic designer with 5 years of experience in visual identity and web design' },
      skills: ['Photoshop', 'Illustrator', 'Figma', 'Branding', 'UI/UX'],
      experience: 'expert',
      hourlyRate: 25,
      categoryId: 'design',
    },
    {
      email: '<EMAIL>',
      firstName: 'سارة',
      lastName: 'المطورة',
      title: { ar: 'مطورة مواقع ويب', en: 'Web Developer' },
      description: { ar: 'مطورة مواقع ويب متخصصة في React و Node.js مع خبرة 4 سنوات', en: 'Web developer specialized in React and Node.js with 4 years of experience' },
      skills: ['React', 'Node.js', 'TypeScript', 'MongoDB', 'AWS'],
      experience: 'expert',
      hourlyRate: 35,
      categoryId: 'programming',
    },
    {
      email: '<EMAIL>',
      firstName: 'عمر',
      lastName: 'الكاتب',
      title: { ar: 'كاتب محتوى ومترجم', en: 'Content Writer & Translator' },
      description: { ar: 'كاتب محتوى ومترجم متخصص في المحتوى التقني والتسويقي', en: 'Content writer and translator specialized in technical and marketing content' },
      skills: ['Arabic Writing', 'English Translation', 'SEO', 'Content Strategy'],
      experience: 'intermediate',
      hourlyRate: 20,
      categoryId: 'writing',
    },
  ];

  for (const expertData of experts) {
    const password = await bcrypt.hash('password123', 12);
    
    const user = await prisma.user.upsert({
      where: { email: expertData.email },
      update: {},
      create: {
        email: expertData.email,
        firstName: expertData.firstName,
        lastName: expertData.lastName,
        role: UserRole.EXPERT,
        status: UserStatus.ACTIVE,
        language: 'ar',
        emailVerified: true,
        passwordHash: password,
        location: {
          governorate: 'damascus',
          city: 'Damascus',
        },
      },
    });

    await prisma.expertProfile.upsert({
      where: { userId: user.id },
      update: {},
      create: {
        userId: user.id,
        title: expertData.title,
        description: expertData.description,
        skills: expertData.skills,
        experience: expertData.experience,
        hourlyRate: expertData.hourlyRate,
        availability: {
          hoursPerWeek: 40,
          timezone: 'Asia/Damascus',
          workingHours: { start: '09:00', end: '17:00' },
          workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        },
        responseTime: 'within_day',
        verified: true,
      },
    });

    // Create sample services for each expert
    const serviceTitle = expertData.categoryId === 'design' 
      ? { ar: 'تصميم لوجو احترافي', en: 'Professional Logo Design' }
      : expertData.categoryId === 'programming'
      ? { ar: 'تطوير موقع ويب متجاوب', en: 'Responsive Website Development' }
      : { ar: 'كتابة محتوى تسويقي', en: 'Marketing Content Writing' };

    const serviceDescription = expertData.categoryId === 'design'
      ? { ar: 'سأقوم بتصميم لوجو احترافي وفريد لعلامتك التجارية مع ملفات عالية الجودة', en: 'I will design a professional and unique logo for your brand with high-quality files' }
      : expertData.categoryId === 'programming'
      ? { ar: 'سأقوم بتطوير موقع ويب متجاوب وسريع باستخدام أحدث التقنيات', en: 'I will develop a responsive and fast website using the latest technologies' }
      : { ar: 'سأكتب محتوى تسويقي جذاب ومؤثر لعلامتك التجارية', en: 'I will write engaging and effective marketing content for your brand' };

    await prisma.service.create({
      data: {
        expertId: (await prisma.expertProfile.findUnique({ where: { userId: user.id } }))!.id,
        title: serviceTitle,
        description: serviceDescription,
        categoryId: expertData.categoryId,
        tags: expertData.skills.slice(0, 3),
        images: [],
        pricing: {
          type: 'fixed',
          basePrice: expertData.categoryId === 'design' ? 50 : expertData.categoryId === 'programming' ? 200 : 30,
          currency: 'USD',
          customPricing: false,
        },
        deliveryTime: expertData.categoryId === 'programming' ? 7 : 3,
        revisions: 2,
        requirements: [],
        addOns: [],
        status: ServiceStatus.ACTIVE,
        seoSlug: `${expertData.firstName.toLowerCase()}-${expertData.categoryId}-service-${nanoid(6)}`,
      },
    });
  }

  // Create sample client users
  console.log('👥 Creating sample client users...');
  
  const clients = [
    {
      email: '<EMAIL>',
      firstName: 'محمد',
      lastName: 'العميل',
    },
    {
      email: '<EMAIL>',
      firstName: 'فاطمة',
      lastName: 'الزبونة',
    },
  ];

  for (const clientData of clients) {
    const password = await bcrypt.hash('password123', 12);
    
    const user = await prisma.user.upsert({
      where: { email: clientData.email },
      update: {},
      create: {
        email: clientData.email,
        firstName: clientData.firstName,
        lastName: clientData.lastName,
        role: UserRole.CLIENT,
        status: UserStatus.ACTIVE,
        language: 'ar',
        emailVerified: true,
        passwordHash: password,
        location: {
          governorate: 'aleppo',
          city: 'Aleppo',
        },
      },
    });

    await prisma.clientProfile.upsert({
      where: { userId: user.id },
      update: {},
      create: {
        userId: user.id,
        companyName: `شركة ${clientData.firstName}`,
        companySize: 'small',
        industry: 'Technology',
      },
    });
  }

  // Create system settings
  console.log('⚙️ Creating system settings...');
  
  const settings = [
    {
      key: 'platform_fee_percentage',
      value: 10,
      description: 'Platform fee percentage',
      category: 'payments',
      public: false,
    },
    {
      key: 'min_payout_amount',
      value: 50,
      description: 'Minimum payout amount in USD',
      category: 'payments',
      public: false,
    },
    {
      key: 'max_file_upload_size',
      value: 10485760, // 10MB
      description: 'Maximum file upload size in bytes',
      category: 'files',
      public: true,
    },
    {
      key: 'supported_languages',
      value: ['ar', 'en'],
      description: 'Supported platform languages',
      category: 'localization',
      public: true,
    },
    {
      key: 'maintenance_mode',
      value: false,
      description: 'Enable maintenance mode',
      category: 'system',
      public: true,
    },
  ];

  for (const setting of settings) {
    await prisma.systemSettings.upsert({
      where: { key: setting.key },
      update: {},
      create: setting,
    });
  }

  console.log('✅ Database seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Database seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
