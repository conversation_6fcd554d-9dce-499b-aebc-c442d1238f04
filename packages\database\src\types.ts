// Extended database types and utilities
import type { Prisma } from '@prisma/client';

// User with relations
export type UserWithProfile = Prisma.UserGetPayload<{
  include: {
    expertProfile: {
      include: {
        education: true;
        certifications: true;
        portfolio: true;
        languages: true;
      };
    };
    clientProfile: true;
  };
}>;

export type ExpertWithProfile = Prisma.ExpertProfileGetPayload<{
  include: {
    user: true;
    services: true;
    education: true;
    certifications: true;
    portfolio: true;
    languages: true;
  };
}>;

// Service with relations
export type ServiceWithDetails = Prisma.ServiceGetPayload<{
  include: {
    expert: {
      include: {
        user: true;
      };
    };
    category: true;
    reviews: {
      include: {
        giver: true;
      };
    };
    bookings: true;
  };
}>;

// Booking with relations
export type BookingWithDetails = Prisma.BookingGetPayload<{
  include: {
    client: true;
    expert: true;
    service: {
      include: {
        expert: {
          include: {
            user: true;
          };
        };
      };
    };
    deliverables: true;
    revisions: true;
    messages: true;
    payments: true;
    reviews: true;
    disputes: true;
  };
}>;

// Message with relations
export type MessageWithDetails = Prisma.MessageGetPayload<{
  include: {
    sender: true;
    receiver: true;
    conversation: true;
    booking: true;
  };
}>;

// Conversation with relations
export type ConversationWithDetails = Prisma.ConversationGetPayload<{
  include: {
    messages: {
      include: {
        sender: true;
      };
      orderBy: {
        createdAt: 'desc';
      };
      take: 1;
    };
  };
}>;

// Review with relations
export type ReviewWithDetails = Prisma.ReviewGetPayload<{
  include: {
    giver: true;
    receiver: true;
    service: true;
    booking: true;
  };
}>;

// Payment with relations
export type PaymentWithDetails = Prisma.PaymentGetPayload<{
  include: {
    payer: true;
    payee: true;
    booking: {
      include: {
        service: true;
      };
    };
  };
}>;

// Search and filtering types
export interface UserSearchFilters {
  role?: Prisma.UserWhereInput['role'];
  status?: Prisma.UserWhereInput['status'];
  verified?: boolean;
  search?: string;
  location?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface ServiceSearchFilters {
  status?: Prisma.ServiceWhereInput['status'];
  categoryId?: string;
  expertId?: string;
  featured?: boolean;
  search?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  deliveryTime?: number;
  tags?: string[];
}

export interface BookingSearchFilters {
  status?: Prisma.BookingWhereInput['status'];
  clientId?: string;
  expertId?: string;
  serviceId?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  priceRange?: {
    min: number;
    max: number;
  };
}

// Pagination types
export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Database operation types
export type CreateUserInput = Prisma.UserCreateInput;
export type UpdateUserInput = Prisma.UserUpdateInput;
export type CreateServiceInput = Prisma.ServiceCreateInput;
export type UpdateServiceInput = Prisma.ServiceUpdateInput;
export type CreateBookingInput = Prisma.BookingCreateInput;
export type UpdateBookingInput = Prisma.BookingUpdateInput;

// Utility types for JSON fields
export interface LocalizedString {
  ar: string;
  en?: string;
}

export interface FileUploadData {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
}

export interface LocationData {
  governorate: string;
  city: string;
  district?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface ServicePricingData {
  type: 'fixed' | 'hourly' | 'package' | 'custom';
  basePrice: number;
  currency: 'USD' | 'SYP';
  packages?: Array<{
    id: string;
    name: LocalizedString;
    description: LocalizedString;
    price: number;
    deliveryTime: number;
    revisions: number;
    features: LocalizedString[];
    popular?: boolean;
  }>;
  hourlyRate?: number;
  customPricing: boolean;
}

export interface AvailabilityData {
  hoursPerWeek: number;
  timezone: string;
  workingHours: {
    start: string;
    end: string;
  };
  workingDays: string[];
  unavailableDates?: Date[];
}
