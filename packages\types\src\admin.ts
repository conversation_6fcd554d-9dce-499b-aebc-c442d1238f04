import { BaseEntity, LocalizedString, PaginatedResponse } from './index';
import { User, UserStatus } from './user';
import { Service, ServiceStatus } from './service';
import { Booking, BookingStatus } from './booking';

export interface AdminUser extends User {
  permissions: AdminPermission[];
  lastLoginAt?: Date;
  loginCount: number;
  createdBy?: string;
}

export type AdminPermission = 
  | 'users.view'
  | 'users.edit'
  | 'users.delete'
  | 'users.suspend'
  | 'services.view'
  | 'services.edit'
  | 'services.approve'
  | 'services.reject'
  | 'bookings.view'
  | 'bookings.edit'
  | 'bookings.cancel'
  | 'payments.view'
  | 'payments.refund'
  | 'analytics.view'
  | 'settings.edit'
  | 'moderation.view'
  | 'moderation.action'
  | 'support.view'
  | 'support.respond';

// Dashboard analytics
export interface AdminDashboard {
  overview: DashboardOverview;
  recentActivity: RecentActivity[];
  alerts: SystemAlert[];
  metrics: DashboardMetrics;
}

export interface DashboardOverview {
  totalUsers: number;
  totalExperts: number;
  totalClients: number;
  activeUsers: number;
  totalServices: number;
  activeServices: number;
  totalBookings: number;
  completedBookings: number;
  totalRevenue: number;
  platformRevenue: number;
  pendingPayouts: number;
}

export interface RecentActivity {
  id: string;
  type: ActivityType;
  description: LocalizedString;
  userId?: string;
  entityId?: string;
  entityType?: string;
  timestamp: Date;
  severity: 'info' | 'warning' | 'error' | 'success';
}

export type ActivityType = 
  | 'user_registered'
  | 'service_created'
  | 'booking_placed'
  | 'payment_processed'
  | 'dispute_opened'
  | 'user_suspended'
  | 'service_approved'
  | 'system_error';

export interface SystemAlert {
  id: string;
  type: AlertType;
  title: LocalizedString;
  message: LocalizedString;
  severity: AlertSeverity;
  status: AlertStatus;
  createdAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  actionRequired: boolean;
  actionUrl?: string;
}

export type AlertType = 
  | 'system_error'
  | 'payment_failure'
  | 'fraud_detection'
  | 'high_dispute_rate'
  | 'server_overload'
  | 'security_breach'
  | 'data_inconsistency';

export type AlertSeverity = 'low' | 'medium' | 'high' | 'critical';
export type AlertStatus = 'open' | 'investigating' | 'resolved' | 'dismissed';

export interface DashboardMetrics {
  userGrowth: MetricTrend[];
  revenueGrowth: MetricTrend[];
  bookingTrends: MetricTrend[];
  conversionRates: ConversionMetrics;
  topCategories: CategoryMetrics[];
  topExperts: ExpertMetrics[];
}

export interface MetricTrend {
  date: string;
  value: number;
  change?: number;
  changePercent?: number;
}

export interface ConversionMetrics {
  visitorToSignup: number;
  signupToFirstBooking: number;
  inquiryToBooking: number;
  bookingToCompletion: number;
}

export interface CategoryMetrics {
  categoryId: string;
  name: LocalizedString;
  serviceCount: number;
  bookingCount: number;
  revenue: number;
  growth: number;
}

export interface ExpertMetrics {
  expertId: string;
  name: string;
  avatar?: string;
  rating: number;
  completedBookings: number;
  revenue: number;
  responseTime: number;
}

// User management
export interface UserManagement {
  users: PaginatedResponse<User>;
  filters: UserFilters;
  actions: UserAction[];
}

export interface UserFilters {
  role?: 'client' | 'expert' | 'admin';
  status?: UserStatus;
  verified?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: string;
  search?: string;
}

export interface UserAction {
  type: UserActionType;
  userId: string;
  reason?: string;
  duration?: number; // for suspensions
  notifyUser?: boolean;
  internalNotes?: string;
}

export type UserActionType = 
  | 'verify'
  | 'suspend'
  | 'unsuspend'
  | 'delete'
  | 'reset_password'
  | 'send_message'
  | 'add_note';

// Service management
export interface ServiceManagement {
  services: PaginatedResponse<Service>;
  filters: ServiceFilters;
  pendingApprovals: Service[];
  rejectedServices: Service[];
}

export interface ServiceFilters {
  status?: ServiceStatus;
  category?: string;
  expertId?: string;
  featured?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  search?: string;
}

export interface ServiceApproval {
  serviceId: string;
  action: 'approve' | 'reject';
  reason?: LocalizedString;
  feedback?: LocalizedString;
  conditions?: string[];
}

// Content moderation
export interface ContentModeration {
  flaggedContent: FlaggedContent[];
  moderationQueue: ModerationQueueItem[];
  moderationRules: ModerationRule[];
  moderationStats: ModerationStats;
}

export interface FlaggedContent extends BaseEntity {
  contentType: ContentType;
  contentId: string;
  reportedBy: string;
  reason: ModerationReason;
  description?: string;
  status: ModerationStatus;
  reviewedBy?: string;
  reviewedAt?: Date;
  action?: ModerationAction;
  content: any; // The actual content being moderated
}

export type ContentType = 
  | 'service'
  | 'user_profile'
  | 'message'
  | 'review'
  | 'portfolio_item'
  | 'booking_requirement';

export type ModerationReason = 
  | 'inappropriate_content'
  | 'spam'
  | 'copyright_violation'
  | 'false_information'
  | 'harassment'
  | 'fraud'
  | 'other';

export type ModerationStatus = 
  | 'pending'
  | 'under_review'
  | 'approved'
  | 'rejected'
  | 'escalated';

export type ModerationAction = 
  | 'no_action'
  | 'content_removed'
  | 'content_edited'
  | 'user_warned'
  | 'user_suspended'
  | 'account_banned';

export interface ModerationQueueItem {
  id: string;
  contentType: ContentType;
  contentId: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  aiScore?: number;
  aiFlags?: string[];
  assignedTo?: string;
  createdAt: Date;
}

export interface ModerationRule {
  id: string;
  name: string;
  description: string;
  contentTypes: ContentType[];
  conditions: RuleCondition[];
  actions: RuleAction[];
  enabled: boolean;
  autoApply: boolean;
}

export interface RuleCondition {
  field: string;
  operator: 'contains' | 'equals' | 'greater_than' | 'less_than' | 'matches';
  value: any;
}

export interface RuleAction {
  type: ModerationAction;
  parameters?: Record<string, any>;
}

export interface ModerationStats {
  totalReports: number;
  pendingReviews: number;
  resolvedToday: number;
  averageResolutionTime: number;
  actionDistribution: Record<ModerationAction, number>;
  reportReasons: Record<ModerationReason, number>;
}

// Financial management
export interface FinancialManagement {
  revenue: RevenueMetrics;
  payouts: PayoutManagement;
  transactions: TransactionHistory;
  disputes: DisputeManagement;
}

export interface RevenueMetrics {
  totalRevenue: number;
  platformRevenue: number;
  expertEarnings: number;
  pendingPayouts: number;
  refunds: number;
  chargebacks: number;
  trends: RevenueTrend[];
}

export interface RevenueTrend {
  date: string;
  revenue: number;
  transactions: number;
  averageOrderValue: number;
}

export interface PayoutManagement {
  pendingPayouts: Payout[];
  scheduledPayouts: Payout[];
  completedPayouts: Payout[];
  failedPayouts: Payout[];
  totalPending: number;
}

export interface Payout {
  id: string;
  expertId: string;
  expertName: string;
  amount: number;
  currency: string;
  method: string;
  status: string;
  scheduledAt: Date;
  processedAt?: Date;
  failureReason?: string;
}

export interface TransactionHistory {
  transactions: Transaction[];
  filters: TransactionFilters;
  summary: TransactionSummary;
}

export interface Transaction {
  id: string;
  type: 'payment' | 'payout' | 'refund' | 'fee';
  amount: number;
  currency: string;
  status: string;
  userId: string;
  bookingId?: string;
  createdAt: Date;
  gateway: string;
  gatewayTransactionId?: string;
}

export interface TransactionFilters {
  type?: string;
  status?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  amountRange?: {
    min: number;
    max: number;
  };
  userId?: string;
  gateway?: string;
}

export interface TransactionSummary {
  totalAmount: number;
  totalTransactions: number;
  successRate: number;
  averageAmount: number;
}

export interface DisputeManagement {
  openDisputes: Dispute[];
  resolvedDisputes: Dispute[];
  escalatedDisputes: Dispute[];
  disputeStats: DisputeStats;
}

export interface Dispute {
  id: string;
  bookingId: string;
  clientId: string;
  expertId: string;
  reason: string;
  amount: number;
  status: string;
  createdAt: Date;
  assignedTo?: string;
  resolution?: string;
}

export interface DisputeStats {
  totalDisputes: number;
  openDisputes: number;
  averageResolutionTime: number;
  resolutionRate: number;
  disputeRate: number;
}
