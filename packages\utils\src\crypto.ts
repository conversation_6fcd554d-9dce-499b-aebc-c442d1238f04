// Crypto utilities

import { createHash, createHmac, randomBytes, createCipher, createDecipher } from 'crypto';

export const generateRandomString = (length: number = 32): string => {
  return randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
};

export const generateRandomBytes = (length: number = 32): Buffer => {
  return randomBytes(length);
};

export const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

export const generateShortId = (length: number = 8): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
};

export const generateNumericCode = (length: number = 6): string => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return Math.floor(Math.random() * (max - min + 1) + min).toString();
};

export const hash = (
  data: string,
  algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512' = 'sha256'
): string => {
  return createHash(algorithm).update(data).digest('hex');
};

export const md5 = (data: string): string => {
  return hash(data, 'md5');
};

export const sha1 = (data: string): string => {
  return hash(data, 'sha1');
};

export const sha256 = (data: string): string => {
  return hash(data, 'sha256');
};

export const sha512 = (data: string): string => {
  return hash(data, 'sha512');
};

export const hmac = (
  data: string,
  secret: string,
  algorithm: 'sha1' | 'sha256' | 'sha512' = 'sha256'
): string => {
  return createHmac(algorithm, secret).update(data).digest('hex');
};

export const hmacSha1 = (data: string, secret: string): string => {
  return hmac(data, secret, 'sha1');
};

export const hmacSha256 = (data: string, secret: string): string => {
  return hmac(data, secret, 'sha256');
};

export const hmacSha512 = (data: string, secret: string): string => {
  return hmac(data, secret, 'sha512');
};

export const base64Encode = (data: string): string => {
  return Buffer.from(data, 'utf8').toString('base64');
};

export const base64Decode = (data: string): string => {
  return Buffer.from(data, 'base64').toString('utf8');
};

export const base64UrlEncode = (data: string): string => {
  return base64Encode(data)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

export const base64UrlDecode = (data: string): string => {
  // Add padding if needed
  let padded = data;
  while (padded.length % 4) {
    padded += '=';
  }
  
  return base64Decode(
    padded
      .replace(/-/g, '+')
      .replace(/_/g, '/')
  );
};

export const hexEncode = (data: string): string => {
  return Buffer.from(data, 'utf8').toString('hex');
};

export const hexDecode = (data: string): string => {
  return Buffer.from(data, 'hex').toString('utf8');
};

export const urlSafeEncode = (data: string): string => {
  return encodeURIComponent(data);
};

export const urlSafeDecode = (data: string): string => {
  return decodeURIComponent(data);
};

// Simple encryption/decryption (not recommended for production)
export const simpleEncrypt = (text: string, password: string): string => {
  try {
    const cipher = createCipher('aes192', password);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  } catch (error) {
    throw new Error('Encryption failed');
  }
};

export const simpleDecrypt = (encryptedText: string, password: string): string => {
  try {
    const decipher = createDecipher('aes192', password);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    throw new Error('Decryption failed');
  }
};

export const generateSalt = (length: number = 16): string => {
  return randomBytes(length).toString('hex');
};

export const hashWithSalt = (
  data: string,
  salt: string,
  algorithm: 'sha256' | 'sha512' = 'sha256'
): string => {
  return hash(data + salt, algorithm);
};

export const verifyHash = (
  data: string,
  salt: string,
  hashedData: string,
  algorithm: 'sha256' | 'sha512' = 'sha256'
): boolean => {
  return hashWithSalt(data, salt, algorithm) === hashedData;
};

export const generateApiKey = (prefix?: string): string => {
  const key = generateRandomString(32);
  return prefix ? `${prefix}_${key}` : key;
};

export const generateSecretKey = (length: number = 64): string => {
  return generateRandomString(length);
};

export const generateToken = (length: number = 32): string => {
  return generateRandomString(length);
};

export const generateOTP = (length: number = 6): string => {
  return generateNumericCode(length);
};

export const generateSessionId = (): string => {
  return generateRandomString(32);
};

export const generateNonce = (): string => {
  return generateRandomString(16);
};

export const generateCSRFToken = (): string => {
  return generateRandomString(32);
};

export const constantTimeCompare = (a: string, b: string): boolean => {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
};

export const maskString = (
  str: string,
  visibleStart: number = 4,
  visibleEnd: number = 4,
  maskChar: string = '*'
): string => {
  if (str.length <= visibleStart + visibleEnd) {
    return str;
  }
  
  const start = str.substring(0, visibleStart);
  const end = str.substring(str.length - visibleEnd);
  const middle = maskChar.repeat(str.length - visibleStart - visibleEnd);
  
  return start + middle + end;
};

export const obfuscateEmail = (email: string): string => {
  const [username, domain] = email.split('@');
  if (!username || !domain) return email;
  
  const maskedUsername = maskString(username, 1, 1);
  return `${maskedUsername}@${domain}`;
};

export const obfuscatePhone = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  return maskString(cleaned, 2, 2);
};

export const obfuscateCardNumber = (cardNumber: string): string => {
  const cleaned = cardNumber.replace(/\s/g, '');
  return maskString(cleaned, 4, 4);
};

export const validateChecksum = (data: string, checksum: string): boolean => {
  const calculatedChecksum = sha256(data);
  return constantTimeCompare(calculatedChecksum, checksum);
};

export const generateChecksum = (data: string): string => {
  return sha256(data);
};

export const secureRandom = (min: number, max: number): number => {
  const range = max - min + 1;
  const bytesNeeded = Math.ceil(Math.log2(range) / 8);
  const maxValue = Math.pow(256, bytesNeeded);
  const threshold = maxValue - (maxValue % range);
  
  let randomValue;
  do {
    const randomBytes = generateRandomBytes(bytesNeeded);
    randomValue = 0;
    for (let i = 0; i < bytesNeeded; i++) {
      randomValue = randomValue * 256 + randomBytes[i];
    }
  } while (randomValue >= threshold);
  
  return min + (randomValue % range);
};

export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = secureRandom(0, i);
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  
  return shuffled;
};

export const generatePassword = (
  length: number = 12,
  includeSymbols: boolean = true
): string => {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  let charset = lowercase + uppercase + numbers;
  if (includeSymbols) {
    charset += symbols;
  }
  
  let password = '';
  
  // Ensure at least one character from each required set
  password += lowercase[secureRandom(0, lowercase.length - 1)];
  password += uppercase[secureRandom(0, uppercase.length - 1)];
  password += numbers[secureRandom(0, numbers.length - 1)];
  
  if (includeSymbols) {
    password += symbols[secureRandom(0, symbols.length - 1)];
  }
  
  // Fill the rest randomly
  for (let i = password.length; i < length; i++) {
    password += charset[secureRandom(0, charset.length - 1)];
  }
  
  // Shuffle the password to avoid predictable patterns
  return shuffleArray(password.split('')).join('');
};
