// Object utilities

export const pick = <T, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> => {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
};

export const omit = <T, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> => {
  const result = { ...obj };
  keys.forEach(key => {
    delete result[key];
  });
  return result;
};

export const isEmpty = (obj: any): boolean => {
  if (obj == null) return true;
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
  if (obj instanceof Map || obj instanceof Set) return obj.size === 0;
  if (typeof obj === 'object') return Object.keys(obj).length === 0;
  return false;
};

export const isEqual = (a: any, b: any): boolean => {
  if (a === b) return true;
  
  if (a == null || b == null) return false;
  
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    return a.every((item, index) => isEqual(item, b[index]));
  }
  
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    return keysA.every(key => isEqual(a[key], b[key]));
  }
  
  return false;
};

export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;
  
  if (obj instanceof Date) return new Date(obj.getTime()) as any;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as any;
  if (obj instanceof Set) return new Set([...obj].map(item => deepClone(item))) as any;
  if (obj instanceof Map) {
    return new Map([...obj].map(([key, value]) => [deepClone(key), deepClone(value)])) as any;
  }
  
  const cloned = {} as T;
  Object.keys(obj).forEach(key => {
    (cloned as any)[key] = deepClone((obj as any)[key]);
  });
  
  return cloned;
};

export const merge = <T extends object, U extends object>(
  target: T,
  source: U
): T & U => {
  return { ...target, ...source };
};

export const deepMerge = <T extends object, U extends object>(
  target: T,
  source: U
): T & U => {
  const result = { ...target } as any;
  
  Object.keys(source).forEach(key => {
    const sourceValue = (source as any)[key];
    const targetValue = result[key];
    
    if (
      typeof sourceValue === 'object' &&
      sourceValue !== null &&
      !Array.isArray(sourceValue) &&
      typeof targetValue === 'object' &&
      targetValue !== null &&
      !Array.isArray(targetValue)
    ) {
      result[key] = deepMerge(targetValue, sourceValue);
    } else {
      result[key] = sourceValue;
    }
  });
  
  return result;
};

export const get = <T>(
  obj: any,
  path: string | string[],
  defaultValue?: T
): T => {
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result == null || typeof result !== 'object') {
      return defaultValue as T;
    }
    result = result[key];
  }
  
  return result === undefined ? defaultValue as T : result;
};

export const set = <T extends object>(
  obj: T,
  path: string | string[],
  value: any
): T => {
  const keys = Array.isArray(path) ? path : path.split('.');
  const result = deepClone(obj);
  let current = result as any;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (current[key] == null || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
  return result;
};

export const has = (obj: any, path: string | string[]): boolean => {
  const keys = Array.isArray(path) ? path : path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current == null || typeof current !== 'object' || !(key in current)) {
      return false;
    }
    current = current[key];
  }
  
  return true;
};

export const unset = <T extends object>(
  obj: T,
  path: string | string[]
): T => {
  const keys = Array.isArray(path) ? path : path.split('.');
  const result = deepClone(obj);
  let current = result as any;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (current[key] == null || typeof current[key] !== 'object') {
      return result;
    }
    current = current[key];
  }
  
  delete current[keys[keys.length - 1]];
  return result;
};

export const keys = <T extends object>(obj: T): (keyof T)[] => {
  return Object.keys(obj) as (keyof T)[];
};

export const values = <T extends object>(obj: T): T[keyof T][] => {
  return Object.values(obj);
};

export const entries = <T extends object>(obj: T): [keyof T, T[keyof T]][] => {
  return Object.entries(obj) as [keyof T, T[keyof T]][];
};

export const fromEntries = <K extends string | number | symbol, V>(
  entries: [K, V][]
): Record<K, V> => {
  return Object.fromEntries(entries) as Record<K, V>;
};

export const invert = <T extends Record<string, string>>(
  obj: T
): Record<T[keyof T], keyof T> => {
  const result = {} as Record<T[keyof T], keyof T>;
  
  Object.entries(obj).forEach(([key, value]) => {
    result[value] = key as keyof T;
  });
  
  return result;
};

export const invertBy = <T extends Record<string, any>, K extends string>(
  obj: T,
  iteratee: (value: T[keyof T]) => K
): Record<K, (keyof T)[]> => {
  const result = {} as Record<K, (keyof T)[]>;
  
  Object.entries(obj).forEach(([key, value]) => {
    const groupKey = iteratee(value);
    if (!result[groupKey]) {
      result[groupKey] = [];
    }
    result[groupKey].push(key as keyof T);
  });
  
  return result;
};

export const mapKeys = <T extends object, K extends string>(
  obj: T,
  iteratee: (value: T[keyof T], key: keyof T) => K
): Record<K, T[keyof T]> => {
  const result = {} as Record<K, T[keyof T]>;
  
  Object.entries(obj).forEach(([key, value]) => {
    const newKey = iteratee(value, key as keyof T);
    result[newKey] = value;
  });
  
  return result;
};

export const mapValues = <T extends object, U>(
  obj: T,
  iteratee: (value: T[keyof T], key: keyof T) => U
): Record<keyof T, U> => {
  const result = {} as Record<keyof T, U>;
  
  Object.entries(obj).forEach(([key, value]) => {
    result[key as keyof T] = iteratee(value, key as keyof T);
  });
  
  return result;
};

export const pickBy = <T extends object>(
  obj: T,
  predicate: (value: T[keyof T], key: keyof T) => boolean
): Partial<T> => {
  const result = {} as Partial<T>;
  
  Object.entries(obj).forEach(([key, value]) => {
    if (predicate(value, key as keyof T)) {
      result[key as keyof T] = value;
    }
  });
  
  return result;
};

export const omitBy = <T extends object>(
  obj: T,
  predicate: (value: T[keyof T], key: keyof T) => boolean
): Partial<T> => {
  const result = {} as Partial<T>;
  
  Object.entries(obj).forEach(([key, value]) => {
    if (!predicate(value, key as keyof T)) {
      result[key as keyof T] = value;
    }
  });
  
  return result;
};

export const transform = <T extends object, U>(
  obj: T,
  iteratee: (accumulator: U, value: T[keyof T], key: keyof T) => void,
  accumulator: U
): U => {
  const result = deepClone(accumulator);
  
  Object.entries(obj).forEach(([key, value]) => {
    iteratee(result, value, key as keyof T);
  });
  
  return result;
};

export const defaults = <T extends object, U extends object>(
  obj: T,
  ...sources: U[]
): T & U => {
  const result = { ...obj } as any;
  
  sources.forEach(source => {
    Object.keys(source).forEach(key => {
      if (result[key] === undefined) {
        result[key] = (source as any)[key];
      }
    });
  });
  
  return result;
};

export const defaultsDeep = <T extends object, U extends object>(
  obj: T,
  ...sources: U[]
): T & U => {
  const result = deepClone(obj) as any;
  
  sources.forEach(source => {
    Object.keys(source).forEach(key => {
      const sourceValue = (source as any)[key];
      const resultValue = result[key];
      
      if (resultValue === undefined) {
        result[key] = deepClone(sourceValue);
      } else if (
        typeof sourceValue === 'object' &&
        sourceValue !== null &&
        !Array.isArray(sourceValue) &&
        typeof resultValue === 'object' &&
        resultValue !== null &&
        !Array.isArray(resultValue)
      ) {
        result[key] = defaultsDeep(resultValue, sourceValue);
      }
    });
  });
  
  return result;
};

export const flatten = (obj: object, prefix: string = ''): Record<string, any> => {
  const result: Record<string, any> = {};
  
  Object.entries(obj).forEach(([key, value]) => {
    const newKey = prefix ? `${prefix}.${key}` : key;
    
    if (
      typeof value === 'object' &&
      value !== null &&
      !Array.isArray(value) &&
      !(value instanceof Date)
    ) {
      Object.assign(result, flatten(value, newKey));
    } else {
      result[newKey] = value;
    }
  });
  
  return result;
};

export const unflatten = (obj: Record<string, any>): any => {
  const result: any = {};
  
  Object.entries(obj).forEach(([key, value]) => {
    const keys = key.split('.');
    let current = result;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (current[k] == null) {
        current[k] = {};
      }
      current = current[k];
    }
    
    current[keys[keys.length - 1]] = value;
  });
  
  return result;
};

export const size = (obj: any): number => {
  if (obj == null) return 0;
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length;
  if (obj instanceof Map || obj instanceof Set) return obj.size;
  if (typeof obj === 'object') return Object.keys(obj).length;
  return 0;
};

export const compact = <T extends object>(obj: T): Partial<T> => {
  return pickBy(obj, (value) => Boolean(value));
};
