// API utilities

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  meta?: Record<string, any>;
}

export const createSuccessResponse = <T>(
  data?: T,
  message?: string,
  meta?: Record<string, any>
): ApiResponse<T> => {
  return {
    success: true,
    message,
    data,
    meta,
  };
};

export const createErrorResponse = (
  message: string,
  errors?: Array<{ field: string; message: string; code: string }>
): ApiResponse => {
  return {
    success: false,
    message,
    errors,
  };
};

export const createPaginatedResponse = <T>(
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  },
  message?: string
): ApiResponse<T[]> => {
  return {
    success: true,
    message,
    data,
    pagination,
  };
};

export const calculatePagination = (
  page: number,
  limit: number,
  total: number
) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
    offset: (page - 1) * limit,
  };
};

export const validatePaginationParams = (
  page?: string | number,
  limit?: string | number
) => {
  const parsedPage = typeof page === 'string' ? parseInt(page) : page || 1;
  const parsedLimit = typeof limit === 'string' ? parseInt(limit) : limit || 10;
  
  return {
    page: Math.max(1, parsedPage),
    limit: Math.min(100, Math.max(1, parsedLimit)),
  };
};

export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, String(item)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });
  
  return searchParams.toString();
};

export const parseQueryString = (queryString: string): Record<string, any> => {
  const params = new URLSearchParams(queryString);
  const result: Record<string, any> = {};
  
  for (const [key, value] of params.entries()) {
    if (result[key]) {
      if (Array.isArray(result[key])) {
        result[key].push(value);
      } else {
        result[key] = [result[key], value];
      }
    } else {
      result[key] = value;
    }
  }
  
  return result;
};

export const extractSortParams = (
  sortBy?: string,
  sortOrder?: string
): { sortBy: string; sortOrder: 'asc' | 'desc' } => {
  return {
    sortBy: sortBy || 'createdAt',
    sortOrder: (sortOrder?.toLowerCase() === 'asc' ? 'asc' : 'desc') as 'asc' | 'desc',
  };
};

export const buildFilterQuery = (filters: Record<string, any>) => {
  const query: Record<string, any> = {};
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value) && value.length > 0) {
        query[key] = { in: value };
      } else if (typeof value === 'string' && value.includes(',')) {
        query[key] = { in: value.split(',').map(v => v.trim()) };
      } else {
        query[key] = value;
      }
    }
  });
  
  return query;
};

export const buildSearchQuery = (
  searchTerm?: string,
  searchFields: string[] = ['title', 'description']
) => {
  if (!searchTerm || searchTerm.trim() === '') {
    return {};
  }
  
  const term = searchTerm.trim();
  
  if (searchFields.length === 1) {
    return {
      [searchFields[0]]: {
        contains: term,
        mode: 'insensitive',
      },
    };
  }
  
  return {
    OR: searchFields.map(field => ({
      [field]: {
        contains: term,
        mode: 'insensitive',
      },
    })),
  };
};

export const buildDateRangeQuery = (
  startDate?: string | Date,
  endDate?: string | Date,
  field: string = 'createdAt'
) => {
  const query: Record<string, any> = {};
  
  if (startDate) {
    query[field] = {
      ...query[field],
      gte: new Date(startDate),
    };
  }
  
  if (endDate) {
    query[field] = {
      ...query[field],
      lte: new Date(endDate),
    };
  }
  
  return Object.keys(query).length > 0 ? query : {};
};

export const buildPriceRangeQuery = (
  minPrice?: number,
  maxPrice?: number,
  field: string = 'price'
) => {
  const query: Record<string, any> = {};
  
  if (minPrice !== undefined) {
    query[field] = {
      ...query[field],
      gte: minPrice,
    };
  }
  
  if (maxPrice !== undefined) {
    query[field] = {
      ...query[field],
      lte: maxPrice,
    };
  }
  
  return Object.keys(query).length > 0 ? query : {};
};

export const sanitizeSearchTerm = (term: string): string => {
  return term
    .trim()
    .replace(/[<>]/g, '')
    .replace(/['"]/g, '')
    .substring(0, 100);
};

export const buildIncludeQuery = (includes?: string | string[]) => {
  if (!includes) return {};
  
  const includeArray = Array.isArray(includes) ? includes : includes.split(',');
  const includeQuery: Record<string, any> = {};
  
  includeArray.forEach(include => {
    const trimmed = include.trim();
    if (trimmed) {
      includeQuery[trimmed] = true;
    }
  });
  
  return includeQuery;
};

export const transformPrismaResult = <T>(
  result: T,
  transforms?: Record<string, (value: any) => any>
): T => {
  if (!transforms || !result) return result;
  
  const transformed = { ...result } as any;
  
  Object.entries(transforms).forEach(([field, transform]) => {
    if (transformed[field] !== undefined) {
      transformed[field] = transform(transformed[field]);
    }
  });
  
  return transformed;
};
