import React from 'react';
import { Trans as I18nTrans } from 'react-i18next';
import { useTranslation, useRTL } from './hooks';

// Enhanced Trans component with RTL support
export interface TransProps {
  i18nKey: string;
  values?: Record<string, any>;
  components?: Record<string, React.ReactElement>;
  ns?: string;
  className?: string;
  style?: React.CSSProperties;
}

export const Trans: React.FC<TransProps> = ({
  i18nKey,
  values,
  components,
  ns,
  className,
  style,
}) => {
  const { direction, textAlign } = useRTL();
  
  const combinedStyle = {
    direction,
    textAlign,
    ...style,
  };
  
  return (
    <I18nTrans
      i18nKey={i18nKey}
      values={values}
      components={components}
      ns={ns}
      className={className}
      style={combinedStyle}
    />
  );
};

// RTL-aware text component
export interface RTLTextProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  as?: keyof JSX.IntrinsicElements;
}

export const RTLText: React.FC<RTLTextProps> = ({
  children,
  className,
  style,
  as: Component = 'span',
}) => {
  const { direction, textAlign } = useRTL();
  
  const combinedStyle = {
    direction,
    textAlign,
    ...style,
  };
  
  return (
    <Component className={className} style={combinedStyle}>
      {children}
    </Component>
  );
};

// Language switcher component
export interface LanguageSwitcherProps {
  className?: string;
  style?: React.CSSProperties;
  showLabels?: boolean;
  variant?: 'dropdown' | 'buttons';
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  className,
  style,
  showLabels = true,
  variant = 'dropdown',
}) => {
  const { t, language, changeLanguage } = useTranslation();
  
  const languages = [
    { code: 'ar', label: 'العربية', flag: '🇸🇾' },
    { code: 'en', label: 'English', flag: '🇺🇸' },
  ];
  
  const handleLanguageChange = (langCode: string) => {
    changeLanguage(langCode);
    
    // Update document attributes
    if (typeof document !== 'undefined') {
      document.documentElement.lang = langCode;
      document.documentElement.dir = langCode === 'ar' ? 'rtl' : 'ltr';
    }
  };
  
  if (variant === 'buttons') {
    return (
      <div className={className} style={style}>
        {languages.map((lang) => (
          <button
            key={lang.code}
            onClick={() => handleLanguageChange(lang.code)}
            className={`language-button ${language === lang.code ? 'active' : ''}`}
            aria-label={`Switch to ${lang.label}`}
          >
            <span className="flag">{lang.flag}</span>
            {showLabels && <span className="label">{lang.label}</span>}
          </button>
        ))}
      </div>
    );
  }
  
  return (
    <select
      value={language}
      onChange={(e) => handleLanguageChange(e.target.value)}
      className={className}
      style={style}
      aria-label={t('common:actions.changeLanguage')}
    >
      {languages.map((lang) => (
        <option key={lang.code} value={lang.code}>
          {lang.flag} {showLabels ? lang.label : ''}
        </option>
      ))}
    </select>
  );
};

// Localized date component
export interface LocalizedDateProps {
  date: Date | string;
  format?: 'short' | 'medium' | 'long' | 'full';
  relative?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const LocalizedDate: React.FC<LocalizedDateProps> = ({
  date,
  format = 'medium',
  relative = false,
  className,
  style,
}) => {
  const { formatDate, formatRelativeTime } = useLocalization();
  
  const formatOptions: Record<string, Intl.DateTimeFormatOptions> = {
    short: { month: 'short', day: 'numeric' },
    medium: { year: 'numeric', month: 'short', day: 'numeric' },
    long: { year: 'numeric', month: 'long', day: 'numeric' },
    full: { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    },
  };
  
  const formattedDate = relative 
    ? formatRelativeTime(date)
    : formatDate(date, formatOptions[format]);
  
  return (
    <time 
      dateTime={new Date(date).toISOString()}
      className={className}
      style={style}
    >
      {formattedDate}
    </time>
  );
};

// Localized number component
export interface LocalizedNumberProps {
  value: number;
  type?: 'number' | 'currency' | 'percentage';
  currency?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  className?: string;
  style?: React.CSSProperties;
}

export const LocalizedNumber: React.FC<LocalizedNumberProps> = ({
  value,
  type = 'number',
  currency = 'USD',
  minimumFractionDigits,
  maximumFractionDigits,
  className,
  style,
}) => {
  const { formatNumber, formatCurrency } = useLocalization();
  
  let formattedValue: string;
  
  switch (type) {
    case 'currency':
      formattedValue = formatCurrency(value, currency);
      break;
    case 'percentage':
      formattedValue = formatNumber(value, {
        style: 'percent',
        minimumFractionDigits,
        maximumFractionDigits,
      });
      break;
    default:
      formattedValue = formatNumber(value, {
        minimumFractionDigits,
        maximumFractionDigits,
      });
  }
  
  return (
    <span className={className} style={style}>
      {formattedValue}
    </span>
  );
};

// Direction provider for nested components
export interface DirectionProviderProps {
  children: React.ReactNode;
  force?: 'ltr' | 'rtl';
}

export const DirectionProvider: React.FC<DirectionProviderProps> = ({
  children,
  force,
}) => {
  const { direction } = useRTL();
  const appliedDirection = force || direction;
  
  return (
    <div dir={appliedDirection} style={{ direction: appliedDirection }}>
      {children}
    </div>
  );
};
