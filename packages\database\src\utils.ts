import type { Prisma } from '@prisma/client';
import { prisma } from './client';
import type { 
  PaginationOptions, 
  PaginatedResult, 
  UserSearchFilters,
  ServiceSearchFilters,
  BookingSearchFilters 
} from './types';

// Pagination utility
export const paginate = async <T>(
  model: any,
  options: PaginationOptions,
  where?: any,
  include?: any
): Promise<PaginatedResult<T>> => {
  const { page, limit, sortBy = 'createdAt', sortOrder = 'desc' } = options;
  const skip = (page - 1) * limit;

  const [data, total] = await Promise.all([
    model.findMany({
      where,
      include,
      skip,
      take: limit,
      orderBy: {
        [sortBy]: sortOrder,
      },
    }),
    model.count({ where }),
  ]);

  const totalPages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
};

// Search utilities
export const buildUserSearchQuery = (filters: UserSearchFilters): Prisma.UserWhereInput => {
  const where: Prisma.UserWhereInput = {};

  if (filters.role) {
    where.role = filters.role;
  }

  if (filters.status) {
    where.status = filters.status;
  }

  if (filters.search) {
    where.OR = [
      { firstName: { contains: filters.search, mode: 'insensitive' } },
      { lastName: { contains: filters.search, mode: 'insensitive' } },
      { email: { contains: filters.search, mode: 'insensitive' } },
    ];
  }

  if (filters.dateRange) {
    where.createdAt = {
      gte: filters.dateRange.start,
      lte: filters.dateRange.end,
    };
  }

  return where;
};

export const buildServiceSearchQuery = (filters: ServiceSearchFilters): Prisma.ServiceWhereInput => {
  const where: Prisma.ServiceWhereInput = {};

  if (filters.status) {
    where.status = filters.status;
  }

  if (filters.categoryId) {
    where.categoryId = filters.categoryId;
  }

  if (filters.expertId) {
    where.expertId = filters.expertId;
  }

  if (filters.featured !== undefined) {
    where.featured = filters.featured;
  }

  if (filters.search) {
    // Note: For JSON fields, we'll need to use raw queries or implement full-text search
    where.OR = [
      { seoSlug: { contains: filters.search, mode: 'insensitive' } },
      { tags: { has: filters.search } },
    ];
  }

  if (filters.rating) {
    where.rating = { gte: filters.rating };
  }

  if (filters.deliveryTime) {
    where.deliveryTime = { lte: filters.deliveryTime };
  }

  if (filters.tags && filters.tags.length > 0) {
    where.tags = { hasSome: filters.tags };
  }

  return where;
};

export const buildBookingSearchQuery = (filters: BookingSearchFilters): Prisma.BookingWhereInput => {
  const where: Prisma.BookingWhereInput = {};

  if (filters.status) {
    where.status = filters.status;
  }

  if (filters.clientId) {
    where.clientId = filters.clientId;
  }

  if (filters.expertId) {
    where.expertId = filters.expertId;
  }

  if (filters.serviceId) {
    where.serviceId = filters.serviceId;
  }

  if (filters.dateRange) {
    where.createdAt = {
      gte: filters.dateRange.start,
      lte: filters.dateRange.end,
    };
  }

  return where;
};

// Slug generation utility
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

export const generateUniqueSlug = async (
  baseSlug: string,
  model: any,
  field: string = 'seoSlug'
): Promise<string> => {
  let slug = generateSlug(baseSlug);
  let counter = 1;

  while (true) {
    const existing = await model.findFirst({
      where: { [field]: slug },
    });

    if (!existing) {
      return slug;
    }

    slug = `${generateSlug(baseSlug)}-${counter}`;
    counter++;
  }
};

// Rating calculation utilities
export const calculateAverageRating = async (
  entityType: 'user' | 'service',
  entityId: string
): Promise<{ rating: number; count: number }> => {
  const where = entityType === 'user' 
    ? { receiverId: entityId }
    : { serviceId: entityId };

  const reviews = await prisma.review.findMany({
    where,
    select: { rating: true },
  });

  if (reviews.length === 0) {
    return { rating: 0, count: 0 };
  }

  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = totalRating / reviews.length;

  return {
    rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
    count: reviews.length,
  };
};

// Update rating for user or service
export const updateEntityRating = async (
  entityType: 'user' | 'service',
  entityId: string
): Promise<void> => {
  const { rating, count } = await calculateAverageRating(entityType, entityId);

  if (entityType === 'user') {
    // Update both expert and client profiles
    await Promise.allSettled([
      prisma.expertProfile.updateMany({
        where: { userId: entityId },
        data: { rating, reviewCount: count },
      }),
      prisma.clientProfile.updateMany({
        where: { userId: entityId },
        data: { rating, reviewCount: count },
      }),
    ]);
  } else {
    await prisma.service.update({
      where: { id: entityId },
      data: { rating, reviewCount: count },
    });
  }
};

// Audit logging utility
export const createAuditLog = async (
  userId: string | null,
  action: string,
  entityType: string,
  entityId: string | null,
  oldValues?: any,
  newValues?: any,
  metadata?: any
): Promise<void> => {
  await prisma.auditLog.create({
    data: {
      userId,
      action,
      entityType,
      entityId,
      oldValues: oldValues ? JSON.stringify(oldValues) : null,
      newValues: newValues ? JSON.stringify(newValues) : null,
      metadata: metadata ? JSON.stringify(metadata) : null,
    },
  });
};

// Analytics utilities
export const recordMetric = async (
  metric: string,
  value: number,
  dimensions?: Record<string, any>,
  timestamp?: Date
): Promise<void> => {
  await prisma.analytics.create({
    data: {
      metric,
      value,
      dimensions: dimensions || {},
      timestamp: timestamp || new Date(),
    },
  });
};

// Cleanup utilities
export const cleanupExpiredSessions = async (): Promise<number> => {
  const result = await prisma.userSession.deleteMany({
    where: {
      expiresAt: {
        lt: new Date(),
      },
    },
  });

  return result.count;
};

export const cleanupOldAuditLogs = async (daysToKeep: number = 90): Promise<number> => {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  const result = await prisma.auditLog.deleteMany({
    where: {
      createdAt: {
        lt: cutoffDate,
      },
    },
  });

  return result.count;
};
