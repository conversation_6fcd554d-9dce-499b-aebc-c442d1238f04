{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../src/hooks.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,IAAI,kBAAkB,EAAE,MAAM,eAAe,CAAC;AACrE,OAAO,EAAE,kBAAkB,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAEjF,gDAAgD;AAChD,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,SAAkB,EAAE,EAAE;IACnD,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAClD,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;IACtC,MAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAE5B,OAAO;QACL,CAAC;QACD,IAAI;QACJ,QAAQ;QACR,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,QAAQ,KAAK,IAAI;QAC3B,SAAS,EAAE,QAAQ,KAAK,IAAI;QAC5B,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC;QACjC,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC;QACjC,cAAc,EAAE,IAAI,CAAC,cAAc;KACpC,CAAC;AACJ,CAAC,CAAC;AAEF,6BAA6B;AAC7B,MAAM,CAAC,MAAM,MAAM,GAAG,GAAG,EAAE;IACzB,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;IACtC,MAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAE5B,OAAO;QACL,KAAK,EAAE,GAAG;QACV,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC;QACjC,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC;QACjC,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK;QAC1C,WAAW,EAAE,CAAC,KAAsB,EAAE,EAAE,CAAC,CAAC;YACxC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,KAAK;SAC5C,CAAC;QACF,SAAS,EAAE,CAAC,KAAsB,EAAE,EAAE,CAAC,CAAC;YACtC,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,KAAK;SAC5C,CAAC;QACF,YAAY,EAAE,CAAC,KAAsB,EAAE,EAAE,CAAC,CAAC;YACzC,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,KAAK;SAC9C,CAAC;QACF,UAAU,EAAE,CAAC,KAAsB,EAAE,EAAE,CAAC,CAAC;YACvC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,KAAK;SAC9C,CAAC;KACH,CAAC;AACJ,CAAC,CAAC;AAEF,gCAAgC;AAChC,MAAM,CAAC,MAAM,eAAe,GAAG,GAAG,EAAE;IAClC,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;IACtC,MAAM,MAAM,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;IAErD,MAAM,UAAU,GAAG,CACjB,IAAmB,EACnB,OAAoC,EAC5B,EAAE;QACV,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CACnB,MAAc,EACd,OAAkC,EAC1B,EAAE;QACV,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CACrB,MAAc,EACd,WAAmB,KAAK,EAChB,EAAE;QACV,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACnC,KAAK,EAAE,UAAU;YACjB,QAAQ;YACR,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACpB,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,CAAC,IAAmB,EAAU,EAAE;QACzD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAEhF,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QAErE,IAAI,aAAa,GAAG,EAAE,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,aAAa,GAAG,IAAI,EAAE,CAAC;YAChC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;QAC/D,CAAC;aAAM,IAAI,aAAa,GAAG,KAAK,EAAE,CAAC;YACjC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;QACL,QAAQ;QACR,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;KACnB,CAAC;AACJ,CAAC,CAAC"}