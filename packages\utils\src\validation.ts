import { z } from 'zod';

// Common validation schemas
export const emailSchema = z.string().email('Invalid email address');

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character');

export const phoneSchema = z
  .string()
  .regex(/^(\+963|0)?[0-9]{8,9}$/, 'Invalid Syrian phone number');

export const urlSchema = z.string().url('Invalid URL');

export const slugSchema = z
  .string()
  .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
  .min(3, 'Slug must be at least 3 characters')
  .max(50, 'Slug must be at most 50 characters');

// User validation schemas
export const userRegistrationSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  phone: phoneSchema.optional(),
  role: z.enum(['client', 'expert']),
  language: z.enum(['ar', 'en']).default('ar'),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms'),
});

export const userLoginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

export const userProfileSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  phone: phoneSchema.optional(),
  bio: z.string().max(500, 'Bio must be at most 500 characters').optional(),
  website: urlSchema.optional(),
  location: z.object({
    governorate: z.string(),
    city: z.string(),
    district: z.string().optional(),
  }).optional(),
});

// Service validation schemas
export const serviceSchema = z.object({
  title: z.object({
    ar: z.string().min(10, 'Arabic title must be at least 10 characters'),
    en: z.string().min(10, 'English title must be at least 10 characters').optional(),
  }),
  description: z.object({
    ar: z.string().min(50, 'Arabic description must be at least 50 characters'),
    en: z.string().min(50, 'English description must be at least 50 characters').optional(),
  }),
  categoryId: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).min(1, 'At least one tag is required').max(10, 'Maximum 10 tags allowed'),
  pricing: z.object({
    type: z.enum(['fixed', 'hourly', 'package', 'custom']),
    basePrice: z.number().min(5, 'Minimum price is $5'),
    currency: z.enum(['USD', 'SYP']).default('USD'),
  }),
  deliveryTime: z.number().min(1, 'Delivery time must be at least 1 day').max(365, 'Maximum delivery time is 365 days'),
  revisions: z.number().min(0, 'Revisions cannot be negative').max(10, 'Maximum 10 revisions'),
});

// Booking validation schemas
export const bookingSchema = z.object({
  serviceId: z.string().min(1, 'Service is required'),
  packageId: z.string().optional(),
  requirements: z.array(z.object({
    questionId: z.string(),
    answer: z.union([z.string(), z.array(z.string())]),
  })),
  customInstructions: z.string().max(1000, 'Instructions must be at most 1000 characters').optional(),
  preferredDeliveryDate: z.date().optional(),
});

// Message validation schemas
export const messageSchema = z.object({
  content: z.string().min(1, 'Message cannot be empty').max(2000, 'Message must be at most 2000 characters'),
  type: z.enum(['text', 'file', 'image']).default('text'),
});

// Review validation schemas
export const reviewSchema = z.object({
  rating: z.number().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
  title: z.string().max(100, 'Title must be at most 100 characters').optional(),
  comment: z.string().max(1000, 'Comment must be at most 1000 characters').optional(),
  wouldRecommend: z.boolean(),
});

// File upload validation
export const fileUploadSchema = z.object({
  filename: z.string().min(1, 'Filename is required'),
  mimeType: z.string().min(1, 'MIME type is required'),
  size: z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
});

// Validation helper functions
export const validateEmail = (email: string): boolean => {
  return emailSchema.safeParse(email).success;
};

export const validatePassword = (password: string): boolean => {
  return passwordSchema.safeParse(password).success;
};

export const validatePhone = (phone: string): boolean => {
  return phoneSchema.safeParse(phone).success;
};

export const validateUrl = (url: string): boolean => {
  return urlSchema.safeParse(url).success;
};

export const validateSlug = (slug: string): boolean => {
  return slugSchema.safeParse(slug).success;
};

// Arabic text validation
export const validateArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
};

// English text validation
export const validateEnglishText = (text: string): boolean => {
  const englishRegex = /^[a-zA-Z\s.,!?'"()-]+$/;
  return englishRegex.test(text);
};

// Syrian phone number validation
export const validateSyrianPhone = (phone: string): boolean => {
  const syrianPhoneRegex = /^(\+963|0)?[0-9]{8,9}$/;
  return syrianPhoneRegex.test(phone);
};

// Credit card validation
export const validateCreditCard = (cardNumber: string): boolean => {
  const cleaned = cardNumber.replace(/\s/g, '');
  const cardRegex = /^[0-9]{13,19}$/;
  
  if (!cardRegex.test(cleaned)) {
    return false;
  }
  
  // Luhn algorithm
  let sum = 0;
  let isEven = false;
  
  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i]);
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
};

// CVV validation
export const validateCVV = (cvv: string, cardType?: string): boolean => {
  if (cardType === 'amex') {
    return /^[0-9]{4}$/.test(cvv);
  }
  return /^[0-9]{3}$/.test(cvv);
};

// Expiry date validation
export const validateExpiryDate = (month: string, year: string): boolean => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  
  const expMonth = parseInt(month);
  const expYear = parseInt(year);
  
  if (expMonth < 1 || expMonth > 12) {
    return false;
  }
  
  if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
    return false;
  }
  
  return true;
};

// File type validation
export const validateFileType = (filename: string, allowedTypes: string[]): boolean => {
  const extension = filename.split('.').pop()?.toLowerCase();
  return extension ? allowedTypes.includes(extension) : false;
};

// Image file validation
export const validateImageFile = (filename: string): boolean => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
  return validateFileType(filename, imageTypes);
};

// Document file validation
export const validateDocumentFile = (filename: string): boolean => {
  const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
  return validateFileType(filename, documentTypes);
};

// Video file validation
export const validateVideoFile = (filename: string): boolean => {
  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
  return validateFileType(filename, videoTypes);
};

// Audio file validation
export const validateAudioFile = (filename: string): boolean => {
  const audioTypes = ['mp3', 'wav', 'ogg', 'aac', 'flac'];
  return validateFileType(filename, audioTypes);
};
