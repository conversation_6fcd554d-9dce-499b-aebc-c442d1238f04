import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema, ZodError } from 'zod';
import { logger } from '../utils/logger';

// Validation result interface
interface ValidationResult {
  success: boolean;
  data?: any;
  errors?: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

/**
 * Generic validation middleware factory
 */
export const validate = (schema: ZodSchema, source: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const dataToValidate = req[source];
      const result = schema.safeParse(dataToValidate);
      
      if (!result.success) {
        const errors = formatZodErrors(result.error);
        
        logger.warn('Validation failed', {
          source,
          errors,
          data: dataToValidate,
          endpoint: req.path,
          method: req.method,
        });
        
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          errors,
        });
      }
      
      // Replace the original data with validated and transformed data
      req[source] = result.data;
      next();
    } catch (error) {
      logger.error('Validation middleware error', { error, source });
      return res.status(500).json({
        success: false,
        message: 'Validation processing failed',
        code: 'VALIDATION_PROCESSING_ERROR',
      });
    }
  };
};

/**
 * Format Zod errors into a more user-friendly format
 */
const formatZodErrors = (error: ZodError): Array<{ field: string; message: string; code: string }> => {
  return error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));
};

/**
 * Validate request body
 */
export const validateBody = (schema: ZodSchema) => validate(schema, 'body');

/**
 * Validate query parameters
 */
export const validateQuery = (schema: ZodSchema) => validate(schema, 'query');

/**
 * Validate URL parameters
 */
export const validateParams = (schema: ZodSchema) => validate(schema, 'params');

/**
 * Validate multiple sources at once
 */
export const validateMultiple = (schemas: {
  body?: ZodSchema;
  query?: ZodSchema;
  params?: ZodSchema;
}) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const errors: Array<{ field: string; message: string; code: string; source: string }> = [];
    
    // Validate body
    if (schemas.body) {
      const bodyResult = schemas.body.safeParse(req.body);
      if (!bodyResult.success) {
        const bodyErrors = formatZodErrors(bodyResult.error).map(err => ({
          ...err,
          source: 'body',
        }));
        errors.push(...bodyErrors);
      } else {
        req.body = bodyResult.data;
      }
    }
    
    // Validate query
    if (schemas.query) {
      const queryResult = schemas.query.safeParse(req.query);
      if (!queryResult.success) {
        const queryErrors = formatZodErrors(queryResult.error).map(err => ({
          ...err,
          source: 'query',
        }));
        errors.push(...queryErrors);
      } else {
        req.query = queryResult.data;
      }
    }
    
    // Validate params
    if (schemas.params) {
      const paramsResult = schemas.params.safeParse(req.params);
      if (!paramsResult.success) {
        const paramsErrors = formatZodErrors(paramsResult.error).map(err => ({
          ...err,
          source: 'params',
        }));
        errors.push(...paramsErrors);
      } else {
        req.params = paramsResult.data;
      }
    }
    
    if (errors.length > 0) {
      logger.warn('Multiple validation failed', {
        errors,
        endpoint: req.path,
        method: req.method,
      });
      
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors,
      });
    }
    
    next();
  };
};

// Common validation schemas
export const commonSchemas = {
  // Pagination
  pagination: z.object({
    page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),
    limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('10'),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  }),
  
  // ID parameter
  idParam: z.object({
    id: z.string().min(1, 'ID is required'),
  }),
  
  // Search query
  search: z.object({
    q: z.string().min(1, 'Search query is required').max(100, 'Search query too long'),
    category: z.string().optional(),
    tags: z.string().optional(),
    minPrice: z.string().transform(Number).pipe(z.number().min(0)).optional(),
    maxPrice: z.string().transform(Number).pipe(z.number().min(0)).optional(),
    location: z.string().optional(),
    sortBy: z.enum(['relevance', 'price', 'rating', 'date']).default('relevance'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  }),
  
  // File upload
  fileUpload: z.object({
    filename: z.string().min(1, 'Filename is required'),
    mimeType: z.string().min(1, 'MIME type is required'),
    size: z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
  }),
  
  // Language preference
  language: z.object({
    lang: z.enum(['ar', 'en']).default('ar'),
  }),
  
  // Location data
  location: z.object({
    governorate: z.string().min(1, 'Governorate is required'),
    city: z.string().min(1, 'City is required'),
    district: z.string().optional(),
    coordinates: z.object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180),
    }).optional(),
  }),
  
  // Date range
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
  }).refine(
    (data) => {
      if (data.startDate && data.endDate) {
        return new Date(data.startDate) <= new Date(data.endDate);
      }
      return true;
    },
    {
      message: 'Start date must be before end date',
      path: ['endDate'],
    }
  ),
  
  // Price range
  priceRange: z.object({
    minPrice: z.number().min(0, 'Minimum price cannot be negative').optional(),
    maxPrice: z.number().min(0, 'Maximum price cannot be negative').optional(),
    currency: z.enum(['USD', 'SYP']).default('USD'),
  }).refine(
    (data) => {
      if (data.minPrice !== undefined && data.maxPrice !== undefined) {
        return data.minPrice <= data.maxPrice;
      }
      return true;
    },
    {
      message: 'Minimum price must be less than or equal to maximum price',
      path: ['maxPrice'],
    }
  ),
};

// Validation helpers
export const validationHelpers = {
  /**
   * Create a validation middleware for pagination
   */
  pagination: () => validateQuery(commonSchemas.pagination),
  
  /**
   * Create a validation middleware for ID parameter
   */
  idParam: () => validateParams(commonSchemas.idParam),
  
  /**
   * Create a validation middleware for search
   */
  search: () => validateQuery(commonSchemas.search),
  
  /**
   * Create a validation middleware for language preference
   */
  language: () => validateQuery(commonSchemas.language),
  
  /**
   * Create a validation middleware for date range
   */
  dateRange: () => validateQuery(commonSchemas.dateRange),
  
  /**
   * Create a validation middleware for price range
   */
  priceRange: () => validateQuery(commonSchemas.priceRange),
  
  /**
   * Validate Arabic text content
   */
  arabicText: (text: string): boolean => {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicRegex.test(text);
  },
  
  /**
   * Validate Syrian phone number
   */
  syrianPhone: (phone: string): boolean => {
    const syrianPhoneRegex = /^(\+963|0)?[0-9]{8,9}$/;
    return syrianPhoneRegex.test(phone);
  },
  
  /**
   * Validate file type
   */
  fileType: (filename: string, allowedTypes: string[]): boolean => {
    const extension = filename.split('.').pop()?.toLowerCase();
    return extension ? allowedTypes.includes(extension) : false;
  },
  
  /**
   * Sanitize HTML content
   */
  sanitizeHtml: (html: string): string => {
    // Basic HTML sanitization - in production, use a proper library like DOMPurify
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  },
  
  /**
   * Validate and normalize email
   */
  normalizeEmail: (email: string): string => {
    return email.toLowerCase().trim();
  },
  
  /**
   * Validate and normalize phone number
   */
  normalizePhone: (phone: string): string => {
    // Remove all non-digit characters except +
    let normalized = phone.replace(/[^\d+]/g, '');
    
    // Add +963 prefix if not present
    if (!normalized.startsWith('+963') && !normalized.startsWith('963')) {
      if (normalized.startsWith('0')) {
        normalized = '+963' + normalized.substring(1);
      } else {
        normalized = '+963' + normalized;
      }
    } else if (normalized.startsWith('963')) {
      normalized = '+' + normalized;
    }
    
    return normalized;
  },
};

export default validate;
