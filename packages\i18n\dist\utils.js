import { getCurrentLanguage, isRTL } from './config';
// Date formatting utilities
export const formatDate = (date, options, language) => {
    const lang = language || getCurrentLanguage();
    const locale = lang === 'ar' ? 'ar-SY' : 'en-US';
    const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    };
    return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options }).format(new Date(date));
};
export const formatTime = (date, options, language) => {
    const lang = language || getCurrentLanguage();
    const locale = lang === 'ar' ? 'ar-SY' : 'en-US';
    const defaultOptions = {
        hour: '2-digit',
        minute: '2-digit',
    };
    return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options }).format(new Date(date));
};
export const formatDateTime = (date, options, language) => {
    const lang = language || getCurrentLanguage();
    const locale = lang === 'ar' ? 'ar-SY' : 'en-US';
    const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    };
    return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options }).format(new Date(date));
};
export const formatRelativeTime = (date, language) => {
    const lang = language || getCurrentLanguage();
    const locale = lang === 'ar' ? 'ar-SY' : 'en-US';
    const now = new Date();
    const targetDate = new Date(date);
    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    if (diffInSeconds < 60) {
        return rtf.format(-diffInSeconds, 'second');
    }
    else if (diffInSeconds < 3600) {
        return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    }
    else if (diffInSeconds < 86400) {
        return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    }
    else if (diffInSeconds < 2592000) {
        return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    }
    else if (diffInSeconds < 31536000) {
        return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    }
    else {
        return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
    }
};
// Number formatting utilities
export const formatNumber = (number, options, language) => {
    const lang = language || getCurrentLanguage();
    const locale = lang === 'ar' ? 'ar-SY' : 'en-US';
    return new Intl.NumberFormat(locale, options).format(number);
};
export const formatCurrency = (amount, currency = 'USD', language) => {
    const lang = language || getCurrentLanguage();
    const locale = lang === 'ar' ? 'ar-SY' : 'en-US';
    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
    }).format(amount);
};
export const formatPercentage = (value, options, language) => {
    const lang = language || getCurrentLanguage();
    const locale = lang === 'ar' ? 'ar-SY' : 'en-US';
    return new Intl.NumberFormat(locale, {
        style: 'percent',
        minimumFractionDigits: 0,
        maximumFractionDigits: 1,
        ...options,
    }).format(value / 100);
};
// Text utilities
export const truncateText = (text, maxLength, suffix = '...') => {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength - suffix.length) + suffix;
};
export const capitalizeFirst = (text) => {
    if (!text)
        return text;
    return text.charAt(0).toUpperCase() + text.slice(1);
};
// RTL utilities
export const getFlexDirection = (language) => {
    return isRTL(language) ? 'row-reverse' : 'row';
};
export const getMarginStart = (value, language) => {
    const prop = isRTL(language) ? 'marginRight' : 'marginLeft';
    return { [prop]: value };
};
export const getMarginEnd = (value, language) => {
    const prop = isRTL(language) ? 'marginLeft' : 'marginRight';
    return { [prop]: value };
};
export const getPaddingStart = (value, language) => {
    const prop = isRTL(language) ? 'paddingRight' : 'paddingLeft';
    return { [prop]: value };
};
export const getPaddingEnd = (value, language) => {
    const prop = isRTL(language) ? 'paddingLeft' : 'paddingRight';
    return { [prop]: value };
};
export const getBorderStart = (value, language) => {
    const prop = isRTL(language) ? 'borderRight' : 'borderLeft';
    return { [prop]: value };
};
export const getBorderEnd = (value, language) => {
    const prop = isRTL(language) ? 'borderLeft' : 'borderRight';
    return { [prop]: value };
};
// Transform styles for RTL
export const transformStyleForRTL = (style, language) => {
    if (!isRTL(language)) {
        return style;
    }
    const transformed = { ...style };
    // Swap left/right properties
    const swapMap = {
        marginLeft: 'marginRight',
        marginRight: 'marginLeft',
        paddingLeft: 'paddingRight',
        paddingRight: 'paddingLeft',
        borderLeft: 'borderRight',
        borderRight: 'borderLeft',
        borderLeftWidth: 'borderRightWidth',
        borderRightWidth: 'borderLeftWidth',
        borderLeftColor: 'borderRightColor',
        borderRightColor: 'borderLeftColor',
        borderLeftStyle: 'borderRightStyle',
        borderRightStyle: 'borderLeftStyle',
        left: 'right',
        right: 'left',
    };
    Object.keys(swapMap).forEach(key => {
        if (style[key] !== undefined) {
            transformed[swapMap[key]] = style[key];
            delete transformed[key];
        }
    });
    // Handle text alignment
    if (style.textAlign === 'left') {
        transformed.textAlign = 'right';
    }
    else if (style.textAlign === 'right') {
        transformed.textAlign = 'left';
    }
    // Handle flex direction
    if (style.flexDirection === 'row') {
        transformed.flexDirection = 'row-reverse';
    }
    else if (style.flexDirection === 'row-reverse') {
        transformed.flexDirection = 'row';
    }
    return transformed;
};
// Validation utilities
export const validateArabicText = (text) => {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicRegex.test(text);
};
export const validateEnglishText = (text) => {
    const englishRegex = /^[a-zA-Z\s.,!?'"()-]+$/;
    return englishRegex.test(text);
};
// Phone number formatting for Syrian context
export const formatSyrianPhoneNumber = (phone) => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('963')) {
        const number = cleaned.substring(3);
        return `+963 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
    }
    else if (cleaned.startsWith('0')) {
        const number = cleaned.substring(1);
        return `0${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
    }
    return phone;
};
// URL utilities
export const generateSlug = (text, language) => {
    const lang = language || getCurrentLanguage();
    if (lang === 'ar') {
        // For Arabic, use transliteration or keep Arabic characters
        return text
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\w-]/g, '')
            .replace(/--+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
    else {
        return text
            .toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/--+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
};
//# sourceMappingURL=utils.js.map