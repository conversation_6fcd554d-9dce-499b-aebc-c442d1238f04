// Database client and utilities
export * from './client';
export * from './types';
export * from './utils';
export * from './seed';

// Re-export Prisma types
export type {
  User,
  ExpertProfile,
  ClientProfile,
  Service,
  ServiceCategory,
  Booking,
  Payment,
  Message,
  Conversation,
  Review,
  Notification,
  UserSession,
  Education,
  Certification,
  PortfolioItem,
  LanguageSkill,
  Dispute,
  Deliverable,
  Revision,
  SystemSettings,
  AuditLog,
  FileUpload,
  Analytics,
  Prisma,
} from '@prisma/client';

// Re-export enums
export {
  UserRole,
  UserStatus,
  ServiceStatus,
  BookingStatus,
  PaymentStatus,
  PaymentMethod,
  NotificationType,
} from '@prisma/client';
