import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import { rateLimitConfig, corsOrigins, isDevelopment } from '../config';
import { redis } from '../utils/redis';
import { logger, logSecurityEvent } from '../utils/logger';
import { createError } from './error';

/**
 * CORS configuration
 */
export const corsMiddleware = cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    // Check if origin is in allowed list
    if (corsOrigins.includes(origin) || corsOrigins.includes('*')) {
      return callback(null, true);
    }
    
    // In development, allow localhost with any port
    if (isDevelopment && origin.includes('localhost')) {
      return callback(null, true);
    }
    
    logSecurityEvent('cors_violation', { origin }, { ip: 'unknown' });
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Client-Version',
    'X-Request-ID',
  ],
  exposedHeaders: [
    'X-Total-Count',
    'X-Page-Count',
    'X-Current-Page',
    'X-Per-Page',
    'X-Rate-Limit-Remaining',
    'X-Rate-Limit-Reset',
  ],
  maxAge: 86400, // 24 hours
});

/**
 * Helmet security headers
 */
export const helmetMiddleware = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});

/**
 * Compression middleware
 */
export const compressionMiddleware = compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
  level: 6,
  threshold: 1024,
});

/**
 * Redis-based rate limiter
 */
class RedisRateLimiter {
  private windowMs: number;
  private maxRequests: number;

  constructor(windowMs: number, maxRequests: number) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
  }

  async isAllowed(key: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    try {
      const now = Date.now();
      const window = Math.floor(now / this.windowMs);
      const redisKey = `rate_limit:${key}:${window}`;

      // Get current count
      const current = await redis.get(redisKey);
      const count = current ? parseInt(current) : 0;

      if (count >= this.maxRequests) {
        const resetTime = (window + 1) * this.windowMs;
        return {
          allowed: false,
          remaining: 0,
          resetTime,
        };
      }

      // Increment counter
      const newCount = count + 1;
      await redis.set(redisKey, newCount.toString(), Math.ceil(this.windowMs / 1000));

      const resetTime = (window + 1) * this.windowMs;
      return {
        allowed: true,
        remaining: this.maxRequests - newCount,
        resetTime,
      };
    } catch (error) {
      logger.error('Redis rate limiter error', { error, key });
      // Fallback to allow request if Redis is down
      return {
        allowed: true,
        remaining: this.maxRequests,
        resetTime: Date.now() + this.windowMs,
      };
    }
  }
}

/**
 * General rate limiting middleware
 */
export const rateLimitMiddleware = rateLimit({
  windowMs: rateLimitConfig.windowMs,
  max: rateLimitConfig.maxRequests,
  message: {
    success: false,
    message: 'Too many requests, please try again later',
    code: 'RATE_LIMIT_EXCEEDED',
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logSecurityEvent('rate_limit_exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
    }, req);

    res.status(429).json({
      success: false,
      message: 'Too many requests, please try again later',
      code: 'RATE_LIMIT_EXCEEDED',
    });
  },
});

/**
 * Strict rate limiting for authentication endpoints
 */
export const authRateLimitMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  skipSuccessfulRequests: true,
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later',
    code: 'AUTH_RATE_LIMIT_EXCEEDED',
  },
  handler: (req, res) => {
    logSecurityEvent('auth_rate_limit_exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      email: req.body?.email,
    }, req);

    res.status(429).json({
      success: false,
      message: 'Too many authentication attempts, please try again later',
      code: 'AUTH_RATE_LIMIT_EXCEEDED',
    });
  },
});

/**
 * IP-based rate limiting
 */
export const createIPRateLimit = (windowMs: number, maxRequests: number) => {
  const limiter = new RedisRateLimiter(windowMs, maxRequests);

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const ip = req.ip || req.connection.remoteAddress || 'unknown';
      const result = await limiter.isAllowed(ip);

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
      });

      if (!result.allowed) {
        logSecurityEvent('ip_rate_limit_exceeded', { ip }, req);
        return res.status(429).json({
          success: false,
          message: 'Too many requests from this IP',
          code: 'IP_RATE_LIMIT_EXCEEDED',
        });
      }

      next();
    } catch (error) {
      logger.error('IP rate limit middleware error', { error });
      next(); // Continue on error
    }
  };
};

/**
 * User-based rate limiting
 */
export const createUserRateLimit = (windowMs: number, maxRequests: number) => {
  const limiter = new RedisRateLimiter(windowMs, maxRequests);

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next();
      }

      const userId = req.user.id;
      const result = await limiter.isAllowed(userId);

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
      });

      if (!result.allowed) {
        logSecurityEvent('user_rate_limit_exceeded', { userId }, req);
        return res.status(429).json({
          success: false,
          message: 'Too many requests from this user',
          code: 'USER_RATE_LIMIT_EXCEEDED',
        });
      }

      next();
    } catch (error) {
      logger.error('User rate limit middleware error', { error });
      next(); // Continue on error
    }
  };
};

/**
 * Request size limiting middleware
 */
export const requestSizeLimit = (maxSize: string = '10mb') => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.get('content-length');
    
    if (contentLength) {
      const size = parseInt(contentLength);
      const maxSizeBytes = parseSize(maxSize);
      
      if (size > maxSizeBytes) {
        logSecurityEvent('request_size_exceeded', {
          size,
          maxSize: maxSizeBytes,
          endpoint: req.path,
        }, req);
        
        return res.status(413).json({
          success: false,
          message: 'Request entity too large',
          code: 'REQUEST_TOO_LARGE',
        });
      }
    }
    
    next();
  };
};

/**
 * Parse size string to bytes
 */
const parseSize = (size: string): number => {
  const units: { [key: string]: number } = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024,
  };
  
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  if (!match) {
    throw new Error('Invalid size format');
  }
  
  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';
  
  return Math.floor(value * units[unit]);
};

/**
 * Security headers middleware
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Remove sensitive headers
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  
  // Add security headers
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
  });
  
  next();
};

/**
 * Request ID middleware
 */
export const requestId = (req: Request, res: Response, next: NextFunction) => {
  const requestId = req.get('X-Request-ID') || generateRequestId();
  req.headers['x-request-id'] = requestId;
  res.set('X-Request-ID', requestId);
  next();
};

/**
 * Generate unique request ID
 */
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Suspicious activity detection
 */
export const suspiciousActivityDetection = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.get('User-Agent') || '';
  const ip = req.ip;
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /hack/i,
    /exploit/i,
  ];
  
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
  
  if (isSuspicious) {
    logSecurityEvent('suspicious_user_agent', {
      userAgent,
      ip,
      endpoint: req.path,
    }, req);
  }
  
  // Check for SQL injection patterns in query parameters
  const queryString = JSON.stringify(req.query);
  const sqlInjectionPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    /(\'|\"|;|--|\*|\/\*|\*\/)/,
  ];
  
  const hasSQLInjection = sqlInjectionPatterns.some(pattern => pattern.test(queryString));
  
  if (hasSQLInjection) {
    logSecurityEvent('sql_injection_attempt', {
      query: req.query,
      ip,
      endpoint: req.path,
    }, req);
    
    return res.status(400).json({
      success: false,
      message: 'Invalid request parameters',
      code: 'INVALID_PARAMETERS',
    });
  }
  
  next();
};

export default {
  cors: corsMiddleware,
  helmet: helmetMiddleware,
  compression: compressionMiddleware,
  rateLimit: rateLimitMiddleware,
  authRateLimit: authRateLimitMiddleware,
  requestSizeLimit,
  securityHeaders,
  requestId,
  suspiciousActivityDetection,
  createIPRateLimit,
  createUserRateLimit,
};
