// Error utilities

export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', true, details);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND');
  }
}

export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 409, 'CONFLICT', true, details);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
  }
}

export class InternalServerError extends AppError {
  constructor(message: string = 'Internal server error') {
    super(message, 500, 'INTERNAL_ERROR', false);
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(message: string = 'Service unavailable') {
    super(message, 503, 'SERVICE_UNAVAILABLE');
  }
}

export const createError = {
  badRequest: (message: string, details?: any) =>
    new AppError(message, 400, 'BAD_REQUEST', true, details),

  unauthorized: (message: string = 'Unauthorized') =>
    new AuthenticationError(message),

  forbidden: (message: string = 'Forbidden') =>
    new AuthorizationError(message),

  notFound: (message: string = 'Not found') =>
    new NotFoundError(message),

  conflict: (message: string, details?: any) =>
    new ConflictError(message, details),

  unprocessableEntity: (message: string, details?: any) =>
    new AppError(message, 422, 'UNPROCESSABLE_ENTITY', true, details),

  tooManyRequests: (message: string = 'Too many requests') =>
    new RateLimitError(message),

  internal: (message: string = 'Internal server error') =>
    new InternalServerError(message),

  serviceUnavailable: (message: string = 'Service unavailable') =>
    new ServiceUnavailableError(message),

  validation: (message: string, details?: any) =>
    new ValidationError(message, details),
};

export const isAppError = (error: any): error is AppError => {
  return error instanceof AppError;
};

export const isOperationalError = (error: any): boolean => {
  return isAppError(error) && error.isOperational;
};

export const getErrorMessage = (error: any): string => {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object' && error.message) {
    return error.message;
  }
  
  return 'Unknown error occurred';
};

export const getErrorCode = (error: any): string => {
  if (isAppError(error)) {
    return error.code;
  }
  
  if (error && typeof error === 'object' && error.code) {
    return error.code;
  }
  
  return 'UNKNOWN_ERROR';
};

export const getErrorStatusCode = (error: any): number => {
  if (isAppError(error)) {
    return error.statusCode;
  }
  
  if (error && typeof error === 'object' && error.statusCode) {
    return error.statusCode;
  }
  
  return 500;
};

export const formatError = (error: any) => {
  return {
    message: getErrorMessage(error),
    code: getErrorCode(error),
    statusCode: getErrorStatusCode(error),
    stack: error instanceof Error ? error.stack : undefined,
    details: isAppError(error) ? error.details : undefined,
  };
};

export const sanitizeError = (error: any, includeStack: boolean = false) => {
  const formatted = formatError(error);
  
  return {
    message: formatted.message,
    code: formatted.code,
    ...(formatted.details && { details: formatted.details }),
    ...(includeStack && formatted.stack && { stack: formatted.stack }),
  };
};

export const wrapAsync = <T extends any[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return (...args: T): Promise<R> => {
    return Promise.resolve(fn(...args)).catch((error) => {
      throw isAppError(error) ? error : new InternalServerError(getErrorMessage(error));
    });
  };
};

export const tryAsync = async <T>(
  fn: () => Promise<T>
): Promise<[T | null, Error | null]> => {
  try {
    const result = await fn();
    return [result, null];
  } catch (error) {
    return [null, error instanceof Error ? error : new Error(String(error))];
  }
};

export const trySync = <T>(
  fn: () => T
): [T | null, Error | null] => {
  try {
    const result = fn();
    return [result, null];
  } catch (error) {
    return [null, error instanceof Error ? error : new Error(String(error))];
  }
};

export const retryAsync = async <T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000,
  backoff: number = 2
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(backoff, attempt - 1)));
    }
  }
  
  throw lastError!;
};

export const timeout = <T>(
  promise: Promise<T>,
  ms: number,
  message: string = 'Operation timed out'
): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new AppError(message, 408, 'TIMEOUT')), ms);
    }),
  ]);
};

export const withTimeout = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  ms: number,
  message?: string
) => {
  return (...args: T): Promise<R> => {
    return timeout(fn(...args), ms, message);
  };
};

export const circuit = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  options: {
    failureThreshold: number;
    resetTimeout: number;
    monitoringPeriod: number;
  } = {
    failureThreshold: 5,
    resetTimeout: 60000,
    monitoringPeriod: 60000,
  }
) => {
  let state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  let failures = 0;
  let lastFailureTime = 0;
  let successes = 0;

  return async (...args: T): Promise<R> => {
    const now = Date.now();

    // Reset failure count if monitoring period has passed
    if (now - lastFailureTime > options.monitoringPeriod) {
      failures = 0;
    }

    // Check if circuit should be half-open
    if (state === 'OPEN' && now - lastFailureTime > options.resetTimeout) {
      state = 'HALF_OPEN';
      successes = 0;
    }

    // Reject immediately if circuit is open
    if (state === 'OPEN') {
      throw new ServiceUnavailableError('Circuit breaker is open');
    }

    try {
      const result = await fn(...args);

      // Reset on success
      if (state === 'HALF_OPEN') {
        successes++;
        if (successes >= 3) {
          state = 'CLOSED';
          failures = 0;
        }
      } else {
        failures = 0;
      }

      return result;
    } catch (error) {
      failures++;
      lastFailureTime = now;

      // Open circuit if failure threshold is reached
      if (failures >= options.failureThreshold) {
        state = 'OPEN';
      } else if (state === 'HALF_OPEN') {
        state = 'OPEN';
      }

      throw error;
    }
  };
};

export const bulkhead = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  maxConcurrent: number = 10
) => {
  let running = 0;
  const queue: Array<{
    args: T;
    resolve: (value: R) => void;
    reject: (error: any) => void;
  }> = [];

  const execute = async (
    args: T,
    resolve: (value: R) => void,
    reject: (error: any) => void
  ) => {
    running++;
    
    try {
      const result = await fn(...args);
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      running--;
      processQueue();
    }
  };

  const processQueue = () => {
    if (queue.length > 0 && running < maxConcurrent) {
      const { args, resolve, reject } = queue.shift()!;
      execute(args, resolve, reject);
    }
  };

  return (...args: T): Promise<R> => {
    return new Promise<R>((resolve, reject) => {
      if (running < maxConcurrent) {
        execute(args, resolve, reject);
      } else {
        queue.push({ args, resolve, reject });
      }
    });
  };
};
