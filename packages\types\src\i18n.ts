// Internationalization types
export interface I18nConfig {
  defaultLanguage: SupportedLanguage;
  supportedLanguages: SupportedLanguage[];
  fallbackLanguage: SupportedLanguage;
  namespaces: string[];
  interpolation: InterpolationConfig;
  detection: LanguageDetectionConfig;
  cache: CacheConfig;
}

export type SupportedLanguage = 'ar' | 'en';
export type SupportedLocale = 'ar-SY' | 'en-US';

export interface InterpolationConfig {
  escapeValue: boolean;
  prefix: string;
  suffix: string;
  formatSeparator: string;
  format?: (value: any, format: string, lng: string) => string;
}

export interface LanguageDetectionConfig {
  order: DetectionMethod[];
  lookupQuerystring: string;
  lookupCookie: string;
  lookupLocalStorage: string;
  lookupSessionStorage: string;
  lookupFromPathIndex: number;
  lookupFromSubdomainIndex: number;
  caches: CacheMethod[];
  excludeCacheFor: string[];
}

export type DetectionMethod = 
  | 'querystring'
  | 'cookie'
  | 'localStorage'
  | 'sessionStorage'
  | 'navigator'
  | 'htmlTag'
  | 'path'
  | 'subdomain';

export type CacheMethod = 'localStorage' | 'cookie' | 'sessionStorage';

export interface CacheConfig {
  enabled: boolean;
  prefix: string;
  expirationTime: number;
  versions: Record<string, string>;
}

// Translation resource types
export interface TranslationResources {
  [language: string]: {
    [namespace: string]: TranslationNamespace;
  };
}

export interface TranslationNamespace {
  [key: string]: string | TranslationNamespace;
}

// Localized content types
export interface LocalizedString {
  ar: string;
  en?: string;
}

export interface LocalizedContent {
  [key: string]: LocalizedString | LocalizedContent;
}

// Translation function types
export interface TranslationFunction {
  (key: string, options?: TranslationOptions): string;
  (key: string, defaultValue: string, options?: TranslationOptions): string;
}

export interface TranslationOptions {
  defaultValue?: string;
  count?: number;
  context?: string;
  replace?: Record<string, any>;
  lng?: SupportedLanguage;
  fallbackLng?: SupportedLanguage;
  ns?: string | string[];
  keySeparator?: string;
  nsSeparator?: string;
  returnObjects?: boolean;
  joinArrays?: string;
  postProcess?: string | string[];
  interpolation?: Partial<InterpolationConfig>;
}

// Pluralization types
export interface PluralRule {
  language: SupportedLanguage;
  rule: (count: number) => number;
}

export interface PluralForms {
  zero?: string;
  one?: string;
  two?: string;
  few?: string;
  many?: string;
  other: string;
}

// Date and time localization
export interface DateTimeFormatOptions {
  locale: SupportedLocale;
  timeZone?: string;
  calendar?: 'gregory' | 'islamic';
  numberingSystem?: 'arab' | 'latn';
  weekday?: 'narrow' | 'short' | 'long';
  era?: 'narrow' | 'short' | 'long';
  year?: 'numeric' | '2-digit';
  month?: 'numeric' | '2-digit' | 'narrow' | 'short' | 'long';
  day?: 'numeric' | '2-digit';
  hour?: 'numeric' | '2-digit';
  minute?: 'numeric' | '2-digit';
  second?: 'numeric' | '2-digit';
  timeZoneName?: 'short' | 'long';
  hour12?: boolean;
}

export interface RelativeTimeFormatOptions {
  locale: SupportedLocale;
  numeric?: 'always' | 'auto';
  style?: 'long' | 'short' | 'narrow';
}

// Number and currency localization
export interface NumberFormatOptions {
  locale: SupportedLocale;
  style?: 'decimal' | 'currency' | 'percent' | 'unit';
  currency?: 'USD' | 'SYP' | 'EUR';
  currencyDisplay?: 'symbol' | 'narrowSymbol' | 'code' | 'name';
  currencySign?: 'standard' | 'accounting';
  useGrouping?: boolean;
  minimumIntegerDigits?: number;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  minimumSignificantDigits?: number;
  maximumSignificantDigits?: number;
  notation?: 'standard' | 'scientific' | 'engineering' | 'compact';
  compactDisplay?: 'short' | 'long';
  signDisplay?: 'auto' | 'never' | 'always' | 'exceptZero';
}

// RTL/LTR support
export interface DirectionConfig {
  language: SupportedLanguage;
  direction: 'ltr' | 'rtl';
  textAlign: 'left' | 'right';
  marginStart: 'marginLeft' | 'marginRight';
  marginEnd: 'marginRight' | 'marginLeft';
  paddingStart: 'paddingLeft' | 'paddingRight';
  paddingEnd: 'paddingRight' | 'paddingLeft';
  borderStart: 'borderLeft' | 'borderRight';
  borderEnd: 'borderRight' | 'borderLeft';
}

// Translation management
export interface TranslationKey {
  key: string;
  namespace: string;
  defaultValue?: string;
  description?: string;
  context?: string;
  maxLength?: number;
  tags?: string[];
}

export interface TranslationEntry {
  key: string;
  namespace: string;
  language: SupportedLanguage;
  value: string;
  status: TranslationStatus;
  lastModified: Date;
  modifiedBy: string;
  approved: boolean;
  approvedBy?: string;
  approvedAt?: Date;
  comments?: TranslationComment[];
}

export type TranslationStatus = 
  | 'draft'
  | 'pending_review'
  | 'approved'
  | 'rejected'
  | 'outdated';

export interface TranslationComment {
  id: string;
  author: string;
  content: string;
  createdAt: Date;
  type: 'suggestion' | 'question' | 'approval' | 'rejection';
}

// Translation validation
export interface TranslationValidation {
  key: string;
  language: SupportedLanguage;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: ValidationSuggestion[];
}

export interface ValidationError {
  type: ValidationErrorType;
  message: string;
  position?: number;
  length?: number;
}

export type ValidationErrorType = 
  | 'missing_interpolation'
  | 'invalid_syntax'
  | 'exceeds_max_length'
  | 'contains_html'
  | 'inappropriate_content';

export interface ValidationWarning {
  type: ValidationWarningType;
  message: string;
  suggestion?: string;
}

export type ValidationWarningType = 
  | 'inconsistent_terminology'
  | 'unusual_length'
  | 'missing_punctuation'
  | 'case_mismatch';

export interface ValidationSuggestion {
  type: SuggestionType;
  original: string;
  suggested: string;
  confidence: number;
  reason: string;
}

export type SuggestionType = 
  | 'grammar'
  | 'spelling'
  | 'terminology'
  | 'style'
  | 'localization';

// Translation automation
export interface AutoTranslationConfig {
  enabled: boolean;
  provider: TranslationProvider;
  sourceLanguage: SupportedLanguage;
  targetLanguages: SupportedLanguage[];
  quality: TranslationQuality;
  postProcessing: PostProcessingRule[];
}

export type TranslationProvider = 
  | 'google_translate'
  | 'microsoft_translator'
  | 'aws_translate'
  | 'deepl'
  | 'custom_model';

export type TranslationQuality = 'fast' | 'balanced' | 'high_quality';

export interface PostProcessingRule {
  type: PostProcessingType;
  pattern: string;
  replacement: string;
  languages?: SupportedLanguage[];
}

export type PostProcessingType = 
  | 'regex_replace'
  | 'terminology_replace'
  | 'format_numbers'
  | 'format_dates'
  | 'rtl_adjustment';

// Context and metadata
export interface TranslationContext {
  screen?: string;
  component?: string;
  feature?: string;
  userType?: 'client' | 'expert' | 'admin';
  platform?: 'mobile' | 'web' | 'email';
  audience?: 'general' | 'technical' | 'marketing';
}

export interface TranslationMetadata {
  wordCount: number;
  characterCount: number;
  complexity: 'simple' | 'medium' | 'complex';
  frequency: 'rare' | 'common' | 'frequent';
  lastUsed?: Date;
  usageCount: number;
  relatedKeys: string[];
}
