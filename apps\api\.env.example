# Database
DATABASE_URL="postgresql://username:password@localhost:5432/freela_syria"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-this-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV="development"
API_VERSION="v1"
CORS_ORIGIN="http://localhost:3000,http://localhost:19006"

# Email
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="Freela Syria"

# File Upload
UPLOAD_MAX_SIZE="10485760"
UPLOAD_ALLOWED_TYPES="jpg,jpeg,png,gif,webp,pdf,doc,docx,txt"
UPLOAD_PATH="uploads"
CDN_URL="https://cdn.freela-syria.com"

# Rate Limiting
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="100"

# Security
BCRYPT_ROUNDS="12"
SESSION_SECRET="your-session-secret-change-this-in-production"

# External APIs
OPENAI_API_KEY="your-openai-api-key"
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# Monitoring
LOG_LEVEL="info"
SENTRY_DSN=""

# Payment
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
PAYPAL_CLIENT_ID=""
PAYPAL_CLIENT_SECRET=""
