{"name": "react-i18next", "version": "13.5.0", "description": "Internationalization for react done right. Using the i18next i18n ecosystem.", "main": "dist/commonjs/index.js", "types": "./index.d.mts", "jsnext:main": "dist/es/index.js", "module": "dist/es/index.js", "keywords": ["i18next", "internationalization", "i18n", "translation", "localization", "l10n", "globalization", "react", "reactjs"], "exports": {"./package.json": "./package.json", ".": {"types": {"require": "./index.d.ts", "import": "./index.d.mts"}, "module": "./dist/es/index.js", "import": "./dist/es/index.js", "require": "./dist/commonjs/index.js", "default": "./dist/es/index.js"}, "./TransWithoutContext": {"types": {"require": "./TransWithoutContext.d.ts", "import": "./TransWithoutContext.d.mts"}, "module": "./dist/es/TransWithoutContext.js", "import": "./dist/es/TransWithoutContext.js", "require": "./dist/commonjs/TransWithoutContext.js", "default": "./dist/es/TransWithoutContext.js"}, "./initReactI18next": {"types": {"require": "./initReactI18next.d.ts", "import": "./initReactI18next.d.mts"}, "module": "./dist/es/initReactI18next.js", "import": "./dist/es/initReactI18next.js", "require": "./dist/commonjs/initReactI18next.js", "default": "./dist/es/initReactI18next.js"}, "./icu.macro": {"types": {"require": "./icu.macro..d.ts", "import": "./icu.macro..d.mts"}, "default": "./icu.macro.js"}}, "homepage": "https://github.com/i18next/react-i18next", "bugs": "https://github.com/i18next/react-i18next/issues", "repository": {"type": "git", "url": "https://github.com/i18next/react-i18next.git"}, "dependencies": {"@babel/runtime": "^7.22.5", "html-parse-stringify": "^3.0.1"}, "devDependencies": {"@babel/cli": "^7.22.5", "@babel/core": "^7.22.9", "@babel/eslint-parser": "^7.22.9", "@babel/plugin-proposal-async-generator-functions": "^7.20.5", "@babel/plugin-proposal-object-rest-spread": "^7.20.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-runtime": "^7.22.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.22.5", "@babel/preset-react": "^7.22.5", "@babel/register": "^7.22.5", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^25.0.1", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "0.4.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^11.2.7", "@testing-library/react-hooks": "^3.4.2", "@types/react": "^18.2.21", "all-contributors-cli": "^6.26.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.8.0", "babel-plugin-macros": "^2.5.0", "babel-plugin-tester": "^6.0.0", "coveralls": "^3.1.1", "cp-cli": "^2.0.0", "cross-env": "^7.0.3", "dtslint": "^4.2.1", "eslint": "^8.42.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest-dom": "^5.0.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-testing-library": "^5.11.0", "husky": "^8.0.3", "i18next": "^23.7.6", "jest": "^24.8.0", "jest-cli": "^24.8.4", "lint-staged": "^8.1.3", "mkdirp": "^1.0.4", "prettier": "2.8.8", "react": "^16.14.0", "react-dom": "^16.14.0", "react-test-renderer": "^16.14.0", "rimraf": "2.6.3", "rollup": "3.25.1", "sinon": "^7.2.3", "tslint": "^6.1.3", "typescript": "5.1.3", "yargs": "^17.7.2"}, "peerDependencies": {"i18next": ">= 23.2.3", "react": ">= 16.8.0"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}, "scripts": {"clean": "rimraf dist && mkdirp dist", "copy": "cp-cli ./dist/umd/react-i18next.min.js ./react-i18next.min.js && cp-cli ./dist/umd/react-i18next.js ./react-i18next.js && echo '{\"type\":\"module\"}' > dist/es/package.json", "build:es": "cross-env BABEL_ENV=jsnext babel src --out-dir dist/es", "build:cjs": "babel src --out-dir dist/commonjs", "build:umd": "rollup -c rollup.config.mjs --format umd && rollup -c rollup.config.mjs --format umd --uglify", "build:amd": "rollup -c rollup.config.mjs --format amd && rollup -c rollup.config.mjs --format amd --uglify", "build:iife": "rollup -c rollup.config.mjs --format iife && rollup -c rollup.config.mjs --format iife --uglify", "build": "npm run clean && npm run build:cjs && npm run build:es && npm run build:umd && npm run build:amd && npm run copy", "fix_dist_package": "node -e 'console.log(`{\"type\":\"module\",\"version\":\"${process.env.npm_package_version}\"}`)' > dist/es/package.json", "preversion": "npm run build && git push", "postversion": "npm run fix_dist_package && git push && git push --tags", "pretest": "npm run test:typescript && npm run test:typescript:noninterop && npm run test:typescript:customtypes", "test": "cross-env BABEL_ENV=development jest --no-cache", "test:watch": "cross-env BABEL_ENV=development jest --no-cache --watch", "test:coverage": "cross-env BABEL_ENV=development jest --no-cache --coverage", "test:lint": "eslint ./src ./test", "test:typescript": "tslint --project tsconfig.json '**/*.{ts,tsx}'", "test:typescript:noninterop": "tslint --project tsconfig.nonEsModuleInterop.json '**/*.{ts,tsx}'", "test:typescript:customtypes": "tslint --project ./test/typescript/custom-types/tsconfig.json '**/*.{ts,tsx}'", "contributors:add": "all-contributors add", "contributors:generate": "all-contributors generate", "prettier": "prettier --write \"{,**/}*.{ts,tsx,js,json,md}\"", "prepare": "husky install"}, "author": "<PERSON> <<EMAIL>> (https://github.com/jamuhl)", "license": "MIT", "jest": {"setupFilesAfterEnv": ["./test/setup.js"], "unmockedModulePathPatterns": ["react"], "testMatch": ["**/test/?(*.)(spec|test).js?(x)"], "modulePathIgnorePatterns": ["<rootDir>/example/"], "collectCoverageFrom": ["**/src/*.{js,jsx}", "*.macro.js", "!**/src/index.js", "!**/src/shallowEqual.js", "!**/node_modules/**", "!**/test/**", "!**/example/**"]}, "lint-staged": {"linters": {"*.{ts,tsx,js,json,md}": ["prettier --write", "git add"]}, "ignore": ["**/dist/**/*.js", "**/react-i18next.js", "**/react-i18next.min.js"]}, "lock": false, "sideEffects": false}