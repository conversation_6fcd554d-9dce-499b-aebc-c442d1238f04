!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).ReactI18next={},e.React)}(this,(function(e,n){"use strict";function t(){return t=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},t.apply(this,arguments)}function i(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var s=i({area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),r=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function o(e){var n={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},t=e.match(/<\/?([^\s]+?)[/\s>]/);if(t&&(n.name=t[1],(s[t[1]]||"/"===e.charAt(e.length-2))&&(n.voidElement=!0),n.name.startsWith("!--"))){var i=e.indexOf("--\x3e");return{type:"comment",comment:-1!==i?e.slice(4,i):""}}for(var o=new RegExp(r),a=null;null!==(a=o.exec(e));)if(a[0].trim())if(a[1]){var c=a[1].trim(),l=[c,""];c.indexOf("=")>-1&&(l=c.split("=")),n.attrs[l[0]]=l[1],o.lastIndex--}else a[2]&&(n.attrs[a[2]]=a[3].trim().substring(1,a[3].length-1));return n}var a=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,c=/^\s*$/,l=Object.create(null);function u(e,n){switch(n.type){case"text":return e+n.content;case"tag":return e+="<"+n.name+(n.attrs?function(e){var n=[];for(var t in e)n.push(t+'="'+e[t]+'"');return n.length?" "+n.join(" "):""}(n.attrs):"")+(n.voidElement?"/>":">"),n.voidElement?e:e+n.children.reduce(u,"")+"</"+n.name+">";case"comment":return e+"\x3c!--"+n.comment+"--\x3e"}}var p={parse:function(e,n){n||(n={}),n.components||(n.components=l);var t,i=[],s=[],r=-1,u=!1;if(0!==e.indexOf("<")){var p=e.indexOf("<");i.push({type:"text",content:-1===p?e:e.substring(0,p)})}return e.replace(a,(function(a,l){if(u){if(a!=="</"+t.name+">")return;u=!1}var p,f="/"!==a.charAt(1),d=a.startsWith("\x3c!--"),g=l+a.length,h=e.charAt(g);if(d){var m=o(a);return r<0?(i.push(m),i):((p=s[r]).children.push(m),i)}if(f&&(r++,"tag"===(t=o(a)).type&&n.components[t.name]&&(t.type="component",u=!0),t.voidElement||u||!h||"<"===h||t.children.push({type:"text",content:e.slice(g,e.indexOf("<",g))}),0===r&&i.push(t),(p=s[r-1])&&p.children.push(t),s[r]=t),(!f||t.voidElement)&&(r>-1&&(t.voidElement||t.name===a.slice(2,-1))&&(r--,t=-1===r?i:s[r]),!u&&"<"!==h&&h)){p=-1===r?i:s[r].children;var y=e.indexOf("<",g),b=e.slice(g,-1===y?void 0:y);c.test(b)&&(b=" "),(y>-1&&r+p.length>=0||" "!==b)&&p.push({type:"text",content:b})}})),i},stringify:function(e){return e.reduce((function(e,n){return e+u("",n)}),"")}};function f(){if(console&&console.warn){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];"string"==typeof n[0]&&(n[0]=`react-i18next:: ${n[0]}`),console.warn(...n)}}const d={};function g(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];"string"==typeof n[0]&&d[n[0]]||("string"==typeof n[0]&&(d[n[0]]=new Date),f(...n))}const h=(e,n)=>()=>{if(e.isInitialized)n();else{const t=()=>{setTimeout((()=>{e.off("initialized",t)}),0),n()};e.on("initialized",t)}};function m(e,n,t){e.loadNamespaces(n,h(e,t))}function y(e,n,t,i){"string"==typeof t&&(t=[t]),t.forEach((n=>{e.options.ns.indexOf(n)<0&&e.options.ns.push(n)})),e.loadLanguages(n,h(e,i))}function b(e){return e.displayName||e.name||("string"==typeof e&&e.length>0?e:"Unknown")}const v=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,x={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},O=e=>x[e];let E,N={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(v,O)};function $(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};N={...N,...e}}function w(){return N}function I(e){E=e}function j(){return E}function k(e,n){if(!e)return!1;const t=e.props?e.props.children:e.children;return n?t.length>0:!!t}function S(e){if(!e)return[];const n=e.props?e.props.children:e.children;return e.props&&e.props.i18nIsDynamicList?R(n):n}function R(e){return Array.isArray(e)?e:[e]}function C(e,t){if(!e)return"";let i="";const s=R(e),r=t.transSupportBasicHtmlNodes&&t.transKeepBasicHtmlNodesFor?t.transKeepBasicHtmlNodesFor:[];return s.forEach(((e,s)=>{if("string"==typeof e)i+=`${e}`;else if(n.isValidElement(e)){const n=Object.keys(e.props).length,o=r.indexOf(e.type)>-1,a=e.props.children;if(!a&&o&&0===n)i+=`<${e.type}/>`;else if(a||o&&0===n)if(e.props.i18nIsDynamicList)i+=`<${s}></${s}>`;else if(o&&1===n&&"string"==typeof a)i+=`<${e.type}>${a}</${e.type}>`;else{const e=C(a,t);i+=`<${s}>${e}</${s}>`}else i+=`<${s}></${s}>`}else if(null===e)f("Trans: the passed in value is invalid - seems you passed in a null child.");else if("object"==typeof e){const{format:n,...t}=e,s=Object.keys(t);if(1===s.length){const e=n?`${s[0]}, ${n}`:s[0];i+=`{{${e}}}`}else f("react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.",e)}else f("Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.",e)})),i}function T(e,i,s,r,o,a){if(""===i)return[];const c=r.transKeepBasicHtmlNodesFor||[],l=i&&new RegExp(c.map((e=>`<${e}`)).join("|")).test(i);if(!e&&!l&&!a)return[i];const u={};!function e(t){R(t).forEach((t=>{"string"!=typeof t&&(k(t)?e(S(t)):"object"!=typeof t||n.isValidElement(t)||Object.assign(u,t))}))}(e);const f=p.parse(`<0>${i}</0>`),d={...u,...o};function g(e,t,i){const s=S(e),r=m(s,t.children,i);return function(e){return"[object Array]"===Object.prototype.toString.call(e)&&e.every((e=>n.isValidElement(e)))}(s)&&0===r.length||e.props&&e.props.i18nIsDynamicList?s:r}function h(e,i,s,r,o){e.dummy?(e.children=i,s.push(n.cloneElement(e,{key:r},o?void 0:i))):s.push(...n.Children.map([e],(e=>{const s={...e.props};return delete s.i18nIsDynamicList,n.createElement(e.type,t({},s,{key:r,ref:e.ref},o?{}:{children:i}))})))}function m(t,i,o){const u=R(t);return R(i).reduce(((t,i,p)=>{const f=i.children&&i.children[0]&&i.children[0].content&&s.services.interpolator.interpolate(i.children[0].content,d,s.language);if("tag"===i.type){let a=u[parseInt(i.name,10)];1!==o.length||a||(a=o[0][i.name]),a||(a={});const y=0!==Object.keys(i.attrs).length?function(e,n){const t={...n};return t.props=Object.assign(e.props,n.props),t}({props:i.attrs},a):a,b=n.isValidElement(y),v=b&&k(i,!0)&&!i.voidElement,x=l&&"object"==typeof y&&y.dummy&&!b,O="object"==typeof e&&null!==e&&Object.hasOwnProperty.call(e,i.name);if("string"==typeof y){const e=s.services.interpolator.interpolate(y,d,s.language);t.push(e)}else if(k(y)||v){h(y,g(y,i,o),t,p)}else if(x){h(y,m(u,i.children,o),t,p)}else if(Number.isNaN(parseFloat(i.name)))if(O){h(y,g(y,i,o),t,p,i.voidElement)}else if(r.transSupportBasicHtmlNodes&&c.indexOf(i.name)>-1)if(i.voidElement)t.push(n.createElement(i.name,{key:`${i.name}-${p}`}));else{const e=m(u,i.children,o);t.push(n.createElement(i.name,{key:`${i.name}-${p}`},e))}else if(i.voidElement)t.push(`<${i.name} />`);else{const e=m(u,i.children,o);t.push(`<${i.name}>${e}</${i.name}>`)}else if("object"!=typeof y||b)h(y,f,t,p,1!==i.children.length||!f);else{const e=i.children[0]?f:null;e&&t.push(e)}}else if("text"===i.type){const e=r.transWrapTextNodes,o=a?r.unescape(s.services.interpolator.interpolate(i.content,d,s.language)):s.services.interpolator.interpolate(i.content,d,s.language);e?t.push(n.createElement(e,{key:`${i.name}-${p}`},o)):t.push(o)}return t}),[])}return S(m([{dummy:!0,children:e||[]}],f,R(e||[]))[0])}function L(e){let{children:t,count:i,parent:s,i18nKey:r,context:o,tOptions:a={},values:c,defaults:l,components:u,ns:p,i18n:f,t:d,shouldUnescape:h,...m}=e;const y=f||j();if(!y)return g("You will need to pass in an i18next instance by using i18nextReactModule"),t;const b=d||y.t.bind(y)||(e=>e);o&&(a.context=o);const v={...w(),...y.options&&y.options.react};let x=p||b.ns||y.options&&y.options.defaultNS;x="string"==typeof x?[x]:x||["translation"];const O=C(t,v),E=l||O||v.transEmptyNodeValue||r,{hashTransKey:N}=v,$=r||(N?N(O||E):O||E);y.options&&y.options.interpolation&&y.options.interpolation.defaultVariables&&(c=c&&Object.keys(c).length>0?{...c,...y.options.interpolation.defaultVariables}:{...y.options.interpolation.defaultVariables});const I=c?a.interpolation:{interpolation:{...a.interpolation,prefix:"#$?",suffix:"?$#"}},k={...a,count:i,...c,...I,defaultValue:E,ns:x},S=$?b($,k):E;u&&Object.keys(u).forEach((e=>{const t=u[e];"function"==typeof t.type||!t.props||!t.props.children||S.indexOf(`${e}/>`)<0&&S.indexOf(`${e} />`)<0||(u[e]=n.createElement((function(){return n.createElement(n.Fragment,null,t)}),null))}));const R=T(u||t,S,y,v,k,h),L=void 0!==s?s:v.defaultTransParent;return L?n.createElement(L,m,R):R}const P={type:"3rdParty",init(e){$(e.options.react),I(e)}},V=n.createContext();class z{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}function A(e){return n=>new Promise((t=>{const i=B();e.getInitialProps?e.getInitialProps(n).then((e=>{t({...e,...i})})):t(i)}))}function B(){const e=j(),n=e.reportNamespaces?e.reportNamespaces.getUsedNamespaces():[],t={},i={};return e.languages.forEach((t=>{i[t]={},n.forEach((n=>{i[t][n]=e.getResourceBundle(t,n)||{}}))})),t.initialI18nStore=i,t.initialLanguage=e.language,t}const F=(e,t)=>{const i=n.useRef();return n.useEffect((()=>{i.current=t?i.current:e}),[e,t]),i.current};function U(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:i}=t,{i18n:s,defaultNS:r}=n.useContext(V)||{},o=i||s||j();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new z),!o){g("You will need to pass in an i18next instance by using initReactI18next");const e=(e,n)=>"string"==typeof n?n:n&&"object"==typeof n&&"string"==typeof n.defaultValue?n.defaultValue:Array.isArray(e)?e[e.length-1]:e,n=[e,{},!1];return n.t=e,n.i18n={},n.ready=!1,n}o.options.react&&void 0!==o.options.react.wait&&g("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...w(),...o.options.react,...t},{useSuspense:c,keyPrefix:l}=a;let u=e||r||o.options&&o.options.defaultNS;u="string"==typeof u?[u]:u||["translation"],o.reportNamespaces.addUsedNamespaces&&o.reportNamespaces.addUsedNamespaces(u);const p=(o.isInitialized||o.initializedStoreOnce)&&u.every((e=>function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.languages&&n.languages.length?void 0!==n.options.ignoreJSONStructure?n.hasLoadedNamespace(e,{lng:t.lng,precheck:(n,i)=>{if(t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!i(n.isLanguageChangingTo,e))return!1}}):function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=n.languages[0],s=!!n.options&&n.options.fallbackLng,r=n.languages[n.languages.length-1];if("cimode"===i.toLowerCase())return!0;const o=(e,t)=>{const i=n.services.backendConnector.state[`${e}|${t}`];return-1===i||2===i};return!(t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!o(n.isLanguageChangingTo,e)||!n.hasResourceBundle(i,e)&&n.services.backendConnector.backend&&(!n.options.resources||n.options.partialBundledLanguages)&&(!o(i,e)||s&&!o(r,e)))}(e,n,t):(g("i18n.languages were undefined or empty",n.languages),!0)}(e,o,a)));function f(){return o.getFixedT(t.lng||null,"fallback"===a.nsMode?u:u[0],l)}const[d,h]=n.useState(f);let b=u.join();t.lng&&(b=`${t.lng}${b}`);const v=F(b),x=n.useRef(!0);n.useEffect((()=>{const{bindI18n:e,bindI18nStore:n}=a;function i(){x.current&&h(f)}return x.current=!0,p||c||(t.lng?y(o,t.lng,u,(()=>{x.current&&h(f)})):m(o,u,(()=>{x.current&&h(f)}))),p&&v&&v!==b&&x.current&&h(f),e&&o&&o.on(e,i),n&&o&&o.store.on(n,i),()=>{x.current=!1,e&&o&&e.split(" ").forEach((e=>o.off(e,i))),n&&o&&n.split(" ").forEach((e=>o.store.off(e,i)))}}),[o,b]);const O=n.useRef(!0);n.useEffect((()=>{x.current&&!O.current&&h(f),O.current=!1}),[o,l]);const E=[d,o,p];if(E.t=d,E.i18n=o,E.ready=p,p)return E;if(!p&&!c)return E;throw new Promise((e=>{t.lng?y(o,t.lng,u,(()=>e())):m(o,u,(()=>e()))}))}function K(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{i18n:s}=i,{i18n:r}=n.useContext(V)||{},o=s||r||j();o.options&&o.options.isClone||(e&&!o.initializedStoreOnce&&(o.services.resourceStore.data=e,o.options.ns=Object.values(e).reduce(((e,n)=>(Object.keys(n).forEach((n=>{e.indexOf(n)<0&&e.push(n)})),e)),o.options.ns),o.initializedStoreOnce=!0,o.isInitialized=!0),t&&!o.initializedLanguageOnce&&(o.changeLanguage(t),o.initializedLanguageOnce=!0))}e.I18nContext=V,e.I18nextProvider=function(e){let{i18n:t,defaultNS:i,children:s}=e;const r=n.useMemo((()=>({i18n:t,defaultNS:i})),[t,i]);return n.createElement(V.Provider,{value:r},s)},e.Trans=function(e){let{children:t,count:i,parent:s,i18nKey:r,context:o,tOptions:a={},values:c,defaults:l,components:u,ns:p,i18n:f,t:d,shouldUnescape:g,...h}=e;const{i18n:m,defaultNS:y}=n.useContext(V)||{},b=f||m||j(),v=d||b&&b.t.bind(b);return L({children:t,count:i,parent:s,i18nKey:r,context:o,tOptions:a,values:c,defaults:l,components:u,ns:p||v&&v.ns||y||b&&b.options&&b.options.defaultNS,i18n:b,t:d,shouldUnescape:g,...h})},e.TransWithoutContext=L,e.Translation=function(e){const{ns:n,children:t,...i}=e,[s,r,o]=U(n,i);return t(s,{i18n:r,lng:r.language},o)},e.composeInitialProps=A,e.date=()=>"",e.getDefaults=w,e.getI18n=j,e.getInitialProps=B,e.initReactI18next=P,e.number=()=>"",e.plural=()=>"",e.select=()=>"",e.selectOrdinal=()=>"",e.setDefaults=$,e.setI18n=I,e.time=()=>"",e.useSSR=K,e.useTranslation=U,e.withSSR=function(){return function(e){function t(t){let{initialI18nStore:i,initialLanguage:s,...r}=t;return K(i,s),n.createElement(e,{...r})}return t.getInitialProps=A(e),t.displayName=`withI18nextSSR(${b(e)})`,t.WrappedComponent=e,t}},e.withTranslation=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(i){function s(s){let{forwardedRef:r,...o}=s;const[a,c,l]=U(e,{...o,keyPrefix:t.keyPrefix}),u={...o,t:a,i18n:c,tReady:l};return t.withRef&&r?u.ref=r:!t.withRef&&r&&(u.forwardedRef=r),n.createElement(i,u)}s.displayName=`withI18nextTranslation(${b(i)})`,s.WrappedComponent=i;return t.withRef?n.forwardRef(((e,t)=>n.createElement(s,Object.assign({},e,{forwardedRef:t})))):s}}}));
