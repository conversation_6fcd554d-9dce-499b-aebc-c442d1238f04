{"name": "@freela/i18n", "version": "1.0.0", "description": "Internationalization package for Freela Syria", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"i18next": "^23.7.6", "react-i18next": "^13.5.0"}, "devDependencies": {"@types/react": "^18.2.45", "typescript": "^5.3.0"}, "peerDependencies": {"react": ">=18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "files": ["dist", "locales"]}