// Formatting utilities

export const formatCurrency = (amount: number, currency: 'USD' | 'SYP' = 'USD'): string => {
  const formatter = new Intl.NumberFormat('ar-SY', {
    style: 'currency',
    currency,
    minimumFractionDigits: currency === 'SYP' ? 0 : 2,
  });
  return formatter.format(amount);
};

export const formatDate = (date: Date | string, locale: 'ar' | 'en' = 'ar'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const localeCode = locale === 'ar' ? 'ar-SY' : 'en-US';
  
  return new Intl.DateTimeFormat(localeCode, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(dateObj);
};

export const formatTime = (date: Date | string, locale: 'ar' | 'en' = 'ar'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const localeCode = locale === 'ar' ? 'ar-SY' : 'en-US';
  
  return new Intl.DateTimeFormat(localeCode, {
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj);
};

export const formatDateTime = (date: Date | string, locale: 'ar' | 'en' = 'ar'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const localeCode = locale === 'ar' ? 'ar-SY' : 'en-US';
  
  return new Intl.DateTimeFormat(localeCode, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj);
};

export const formatRelativeTime = (date: Date | string, locale: 'ar' | 'en' = 'ar'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  const rtf = new Intl.RelativeTimeFormat(locale === 'ar' ? 'ar' : 'en', { numeric: 'auto' });
  
  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  } else if (diffInSeconds < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
  } else if (diffInSeconds < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
  } else if (diffInSeconds < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
  } else if (diffInSeconds < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
  }
};

export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

export const formatPhoneNumber = (phone: string): string => {
  // Format Syrian phone numbers
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.startsWith('963')) {
    const number = cleaned.substring(3);
    return `+963 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
  } else if (cleaned.startsWith('0')) {
    const number = cleaned.substring(1);
    return `0${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
  }
  
  return phone;
};

export const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - suffix.length) + suffix;
};

export const capitalizeFirst = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${(value * 100).toFixed(decimals)}%`;
};

export const formatNumber = (num: number, locale: 'ar' | 'en' = 'ar'): string => {
  const localeCode = locale === 'ar' ? 'ar-SY' : 'en-US';
  return new Intl.NumberFormat(localeCode).format(num);
};
