import { useTranslation as useI18nTranslation } from 'react-i18next';
import { getCurrentLanguage, isRTL, getDirection, getTextAlign } from './config';
// Enhanced useTranslation hook with RTL support
export const useTranslation = (namespace) => {
    const { t, i18n } = useI18nTranslation(namespace);
    const language = getCurrentLanguage();
    const rtl = isRTL(language);
    return {
        t,
        i18n,
        language,
        isRTL: rtl,
        isArabic: language === 'ar',
        isEnglish: language === 'en',
        direction: getDirection(language),
        textAlign: getTextAlign(language),
        changeLanguage: i18n.changeLanguage,
    };
};
// Hook for RTL-aware styling
export const useRTL = () => {
    const language = getCurrentLanguage();
    const rtl = isRTL(language);
    return {
        isRTL: rtl,
        direction: getDirection(language),
        textAlign: getTextAlign(language),
        flexDirection: rtl ? 'row-reverse' : 'row',
        marginStart: (value) => ({
            [rtl ? 'marginRight' : 'marginLeft']: value,
        }),
        marginEnd: (value) => ({
            [rtl ? 'marginLeft' : 'marginRight']: value,
        }),
        paddingStart: (value) => ({
            [rtl ? 'paddingRight' : 'paddingLeft']: value,
        }),
        paddingEnd: (value) => ({
            [rtl ? 'paddingLeft' : 'paddingRight']: value,
        }),
    };
};
// Hook for localized formatting
export const useLocalization = () => {
    const language = getCurrentLanguage();
    const locale = language === 'ar' ? 'ar-SY' : 'en-US';
    const formatDate = (date, options) => {
        return new Intl.DateTimeFormat(locale, options).format(new Date(date));
    };
    const formatNumber = (number, options) => {
        return new Intl.NumberFormat(locale, options).format(number);
    };
    const formatCurrency = (amount, currency = 'USD') => {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency,
            minimumFractionDigits: 0,
        }).format(amount);
    };
    const formatRelativeTime = (date) => {
        const now = new Date();
        const targetDate = new Date(date);
        const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);
        const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
        if (diffInSeconds < 60) {
            return rtf.format(-diffInSeconds, 'second');
        }
        else if (diffInSeconds < 3600) {
            return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
        }
        else if (diffInSeconds < 86400) {
            return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
        }
        else {
            return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
        }
    };
    return {
        language,
        locale,
        formatDate,
        formatNumber,
        formatCurrency,
        formatRelativeTime,
    };
};
//# sourceMappingURL=hooks.js.map