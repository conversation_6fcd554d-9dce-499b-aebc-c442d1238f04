{"name": "i18next", "version": "23.16.8", "description": "i18next internationalization framework", "main": "./dist/cjs/i18next.js", "module": "./dist/esm/i18next.js", "types": "./index.d.ts", "typesVersions": {"<5.0": {"typescript/t.d.ts": ["typescript/t.v4.d.ts"], "index.d.ts": ["index.v4.d.ts"]}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./index.d.mts", "default": "./dist/esm/i18next.js"}, "require": {"types": "./index.d.ts", "default": "./dist/cjs/i18next.js"}}}, "keywords": ["i18next", "internationalization", "i18n", "translation", "localization", "l10n", "globalization", "gettext"], "homepage": "https://www.i18next.com", "bugs": "https://github.com/i18next/i18next/issues", "repository": {"type": "git", "url": "https://github.com/i18next/i18next.git"}, "funding": [{"type": "individual", "url": "https://locize.com"}, {"type": "individual", "url": "https://locize.com/i18next.html"}, {"type": "individual", "url": "https://www.i18next.com/how-to/faq#i18next-is-awesome.-how-can-i-support-the-project"}], "dependencies": {"@babel/runtime": "^7.23.2"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/plugin-proposal-async-generator-functions": "^7.20.7", "@babel/plugin-transform-modules-commonjs": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.3", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.23.3", "@babel/preset-react": "^7.23.3", "@babel/register": "^7.22.15", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "0.4.4", "@types/node": "20.14.10", "@typescript-eslint/eslint-plugin": "7.8.0", "@typescript-eslint/parser": "7.8.0", "@vitest/coverage-v8": "2.0.2", "babelify": "^10.0.0", "coveralls": "3.1.1", "cpy-cli": "^5.0.0", "eslint": "8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "2.29.0", "gh-release": "7.0.2", "husky": "^8.0.3", "i18next-browser-languagedetector": "7.1.0", "i18next-fs-backend": "2.2.0", "i18next-http-backend": "2.3.1", "i18next-localstorage-cache": "1.1.1", "i18next-sprintf-postprocessor": "0.2.2", "lint-staged": "^15.0.2", "prettier": "^3.0.3", "rimraf": "5.0.5", "rollup": "^4.3.0", "sinon": "17.0.1", "typescript": "5.4.4", "vitest": "2.0.2"}, "scripts": {"lint": "eslint src typescript test \"./*.{ts,mts,mjs}\"", "format": "prettier \"{,**/}*.{ts,tsx,mts,js,mjs,json,md}\" --check", "format:fix": "prettier \"{,**/}*.{ts,tsx,mts,js,mjs,json,md}\" --write", "pretest": "npm run generate_ts_v4_index", "pretest:typescript": "npm run generate_ts_v4_index", "test": "vitest --run", "test:coverage": "vitest --coverage --run", "test:runtime": "vitest --project runtime", "test:compatibility": "vitest --project compatibility", "test:typescript": "vitest --workspace vitest.workspace.typescript.mts", "test:local": "vitest --workspace vitest.workspace.local.mts", "build": "rimraf dist && rollup -c && echo '{\"type\":\"module\"}' > dist/esm/package.json && cpy \"./dist/umd/*.js\" ./", "generate_ts_v4_index": "cp index.d.ts index.v4.d.ts && node -e \"fs.writeFileSync('index.v4.d.ts', fs.readFileSync('index.v4.d.ts').toString().replace(/t.js/g, 't.v4.js').replace(/export type \\* /g, '// export type * '))\"", "fix_dist_package": "node -e 'console.log(`{\"type\":\"module\",\"version\":\"${process.env.npm_package_version}\"}`)' > dist/esm/package.json", "preversion": "npm run test && npm run build && git push", "postversion": "npm run fix_dist_package && git push && git push --tags && npm run release", "prepare": "husky install", "release": "gh-release"}, "author": "<PERSON> <<EMAIL>> (https://github.com/jamuhl)", "license": "MIT", "lint-staged": {"*.{ts,tsx,mts,js,mjs,json,md}": "prettier --write"}}