import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Trans as I18nTrans } from 'react-i18next';
import { useTranslation, useRTL } from './hooks';
export const Trans = ({ i18nKey, values, components, ns, className, style, }) => {
    const { direction, textAlign } = useRTL();
    const combinedStyle = {
        direction,
        textAlign,
        ...style,
    };
    return (_jsx(I18nTrans, { i18nKey: i18nKey, values: values, components: components, ns: ns, className: className, style: combinedStyle }));
};
export const RTLText = ({ children, className, style, as: Component = 'span', }) => {
    const { direction, textAlign } = useRTL();
    const combinedStyle = {
        direction,
        textAlign,
        ...style,
    };
    return (_jsx(Component, { className: className, style: combinedStyle, children: children }));
};
export const LanguageSwitcher = ({ className, style, showLabels = true, variant = 'dropdown', }) => {
    const { t, language, changeLanguage } = useTranslation();
    const languages = [
        { code: 'ar', label: 'العربية', flag: '🇸🇾' },
        { code: 'en', label: 'English', flag: '🇺🇸' },
    ];
    const handleLanguageChange = (langCode) => {
        changeLanguage(langCode);
        // Update document attributes
        if (typeof document !== 'undefined') {
            document.documentElement.lang = langCode;
            document.documentElement.dir = langCode === 'ar' ? 'rtl' : 'ltr';
        }
    };
    if (variant === 'buttons') {
        return (_jsx("div", { className: className, style: style, children: languages.map((lang) => (_jsxs("button", { onClick: () => handleLanguageChange(lang.code), className: `language-button ${language === lang.code ? 'active' : ''}`, "aria-label": `Switch to ${lang.label}`, children: [_jsx("span", { className: "flag", children: lang.flag }), showLabels && _jsx("span", { className: "label", children: lang.label })] }, lang.code))) }));
    }
    return (_jsx("select", { value: language, onChange: (e) => handleLanguageChange(e.target.value), className: className, style: style, "aria-label": t('common:actions.changeLanguage'), children: languages.map((lang) => (_jsxs("option", { value: lang.code, children: [lang.flag, " ", showLabels ? lang.label : ''] }, lang.code))) }));
};
export const LocalizedDate = ({ date, format = 'medium', relative = false, className, style, }) => {
    const { t } = useTranslation();
    const formatOptions = {
        short: { month: 'short', day: 'numeric' },
        medium: { year: 'numeric', month: 'short', day: 'numeric' },
        long: { year: 'numeric', month: 'long', day: 'numeric' },
        full: {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        },
    };
    const formattedDate = relative
        ? formatRelativeTime(date)
        : formatDate(date, formatOptions[format]);
    return (_jsx("time", { dateTime: new Date(date).toISOString(), className: className, style: style, children: formattedDate }));
};
export const LocalizedNumber = ({ value, type = 'number', currency = 'USD', minimumFractionDigits, maximumFractionDigits, className, style, }) => {
    const { t } = useTranslation();
    let formattedValue;
    switch (type) {
        case 'currency':
            formattedValue = formatCurrency(value, currency);
            break;
        case 'percentage':
            formattedValue = formatNumber(value, {
                style: 'percent',
                minimumFractionDigits,
                maximumFractionDigits,
            });
            break;
        default:
            formattedValue = formatNumber(value, {
                minimumFractionDigits,
                maximumFractionDigits,
            });
    }
    return (_jsx("span", { className: className, style: style, children: formattedValue }));
};
export const DirectionProvider = ({ children, force, }) => {
    const { direction } = useRTL();
    const appliedDirection = force || direction;
    return (_jsx("div", { dir: appliedDirection, style: { direction: appliedDirection }, children: children }));
};
//# sourceMappingURL=components.js.map