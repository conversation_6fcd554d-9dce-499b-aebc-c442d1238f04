import express from 'express';
import { config, isDevelopment } from './config';
import { logger, requestLogger } from './utils/logger';
import { connectDatabase } from '@freela/database';
import { redis } from './utils/redis';

// Middleware imports
import securityMiddleware from './middleware/security';
import { errorHand<PERSON>, notFoundHandler } from './middleware/error';

// Route imports
import authRoutes from './routes/auth';

// Swagger imports
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

class App {
  public app: express.Application;

  constructor() {
    this.app = express();
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeSwagger();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', 1);

    // Security middleware
    this.app.use(securityMiddleware.requestId);
    this.app.use(securityMiddleware.securityHeaders);
    this.app.use(securityMiddleware.helmet);
    this.app.use(securityMiddleware.cors);
    this.app.use(securityMiddleware.compression);
    this.app.use(securityMiddleware.suspiciousActivityDetection);

    // Rate limiting
    this.app.use(securityMiddleware.rateLimit);

    // Request parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use(requestLogger);

    // Health check endpoint (before rate limiting)
    this.app.get('/health', this.healthCheck);
  }

  private initializeRoutes(): void {
    const apiVersion = config.API_VERSION;

    // API routes
    this.app.use(`/api/${apiVersion}/auth`, authRoutes);

    // Root endpoint
    this.app.get('/', (req, res) => {
      res.json({
        success: true,
        message: 'Freela Syria API Server',
        version: apiVersion,
        timestamp: new Date().toISOString(),
        environment: config.NODE_ENV,
      });
    });

    // API info endpoint
    this.app.get(`/api/${apiVersion}`, (req, res) => {
      res.json({
        success: true,
        message: 'Freela Syria API',
        version: apiVersion,
        endpoints: {
          auth: `/api/${apiVersion}/auth`,
          docs: `/api/${apiVersion}/docs`,
          health: '/health',
        },
        timestamp: new Date().toISOString(),
      });
    });
  }

  private initializeSwagger(): void {
    if (!isDevelopment) return;

    const swaggerOptions = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'Freela Syria API',
          version: '1.0.0',
          description: 'AI-Powered Freelance Marketplace API for Syrian Experts',
          contact: {
            name: 'Freela Syria Team',
            email: '<EMAIL>',
          },
          license: {
            name: 'MIT',
            url: 'https://opensource.org/licenses/MIT',
          },
        },
        servers: [
          {
            url: `http://localhost:${config.PORT}/api/${config.API_VERSION}`,
            description: 'Development server',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
        tags: [
          {
            name: 'Authentication',
            description: 'User authentication and authorization endpoints',
          },
          {
            name: 'Users',
            description: 'User management endpoints',
          },
          {
            name: 'Experts',
            description: 'Expert profile management endpoints',
          },
          {
            name: 'Services',
            description: 'Service management endpoints',
          },
          {
            name: 'Bookings',
            description: 'Booking management endpoints',
          },
          {
            name: 'Payments',
            description: 'Payment processing endpoints',
          },
          {
            name: 'Chat',
            description: 'Messaging and chat endpoints',
          },
          {
            name: 'Admin',
            description: 'Administrative endpoints',
          },
        ],
      },
      apis: ['./src/routes/*.ts'], // Path to the API docs
    };

    const swaggerSpec = swaggerJsdoc(swaggerOptions);

    // Swagger UI
    this.app.use(
      `/api/${config.API_VERSION}/docs`,
      swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, {
        explorer: true,
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'Freela Syria API Documentation',
      })
    );

    // Swagger JSON
    this.app.get(`/api/${config.API_VERSION}/docs.json`, (req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(swaggerSpec);
    });

    logger.info(`Swagger documentation available at http://localhost:${config.PORT}/api/${config.API_VERSION}/docs`);
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  private healthCheck = async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      // Check database connection
      const dbHealth = await this.checkDatabaseHealth();
      
      // Check Redis connection
      const redisHealth = await this.checkRedisHealth();

      const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.NODE_ENV,
        version: config.API_VERSION,
        services: {
          database: dbHealth ? 'healthy' : 'unhealthy',
          redis: redisHealth ? 'healthy' : 'unhealthy',
        },
        memory: {
          used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
          total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
        },
      };

      const statusCode = dbHealth && redisHealth ? 200 : 503;
      res.status(statusCode).json(health);
    } catch (error) {
      logger.error('Health check failed', { error });
      res.status(503).json({
        status: 'error',
        timestamp: new Date().toISOString(),
        message: 'Health check failed',
      });
    }
  };

  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      const { checkDatabaseHealth } = await import('@freela/database');
      return await checkDatabaseHealth();
    } catch (error) {
      logger.error('Database health check failed', { error });
      return false;
    }
  }

  private async checkRedisHealth(): Promise<boolean> {
    try {
      return await redis.ping();
    } catch (error) {
      logger.error('Redis health check failed', { error });
      return false;
    }
  }

  public async initialize(): Promise<void> {
    try {
      // Connect to database
      await connectDatabase();
      logger.info('✅ Database connected');

      // Connect to Redis
      await redis.connect();
      logger.info('✅ Redis connected');

      logger.info('✅ Application initialized successfully');
    } catch (error) {
      logger.error('❌ Application initialization failed', { error });
      throw error;
    }
  }

  public async shutdown(): Promise<void> {
    try {
      // Disconnect from Redis
      await redis.disconnect();
      logger.info('✅ Redis disconnected');

      // Database disconnection is handled by Prisma client
      logger.info('✅ Application shutdown completed');
    } catch (error) {
      logger.error('❌ Application shutdown failed', { error });
      throw error;
    }
  }
}

export default App;
