{"navigation": {"home": "Home", "search": "Search", "services": "Services", "bookings": "Bookings", "messages": "Messages", "profile": "Profile", "dashboard": "Dashboard", "settings": "Settings", "help": "Help", "logout": "Logout"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "confirm": "Confirm", "retry": "Retry", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "share": "Share", "copy": "Copy", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh"}, "status": {"loading": "Loading...", "success": "Success", "error": "Error", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "active": "Active", "inactive": "Inactive", "approved": "Approved", "rejected": "Rejected", "draft": "Draft"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "lastWeek": "Last week", "thisMonth": "This month", "lastMonth": "Last month", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "month": "month", "months": "months", "year": "year", "years": "years"}, "weekdays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "currency": {"usd": "US Dollar", "syp": "Syrian Pound", "symbol": {"usd": "$", "syp": "SYP"}}, "location": {"governorates": {"damascus": "Damascus", "aleppo": "Aleppo", "homs": "Homs", "hama": "<PERSON><PERSON>", "latakia": "Latakia", "tartus": "<PERSON><PERSON><PERSON>", "idlib": "<PERSON><PERSON><PERSON>", "daraa": "<PERSON><PERSON>", "sweida": "As<PERSON><PERSON><PERSON><PERSON>", "quneitra": "Quneitra", "raqqa": "<PERSON><PERSON><PERSON>", "deir_ez_zor": "<PERSON>ir ez-Zor", "hasaka": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "damascus_countryside": "Damascus Countryside"}}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "password": "Password must be at least 8 characters", "confirmPassword": "Passwords do not match", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be at most {{max}} characters", "min": "Must be at least {{min}}", "max": "Must be at most {{max}}", "numeric": "Must be a number", "url": "Please enter a valid URL"}, "errors": {"general": "An unexpected error occurred", "network": "Network connection error", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "notFound": "Page not found", "serverError": "Server error", "timeout": "Request timeout", "offline": "No internet connection"}, "pagination": {"previous": "Previous", "next": "Next", "first": "First", "last": "Last", "page": "Page", "of": "of", "showing": "Showing", "to": "to", "results": "results"}, "fileUpload": {"dragDrop": "Drag files here or click to select", "selectFile": "Select file", "selectFiles": "Select files", "maxSize": "Max size: {{size}}", "allowedTypes": "Allowed types: {{types}}", "uploading": "Uploading...", "uploadSuccess": "File uploaded successfully", "uploadError": "Failed to upload file", "fileTooLarge": "File is too large", "invalidType": "File type not supported"}}