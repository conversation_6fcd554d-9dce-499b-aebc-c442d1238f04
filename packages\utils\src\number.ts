// Number utilities

export const isNumber = (value: any): value is number => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
};

export const isInteger = (value: any): value is number => {
  return isNumber(value) && Number.isInteger(value);
};

export const isFloat = (value: any): value is number => {
  return isNumber(value) && !Number.isInteger(value);
};

export const isPositive = (value: number): boolean => {
  return value > 0;
};

export const isNegative = (value: number): boolean => {
  return value < 0;
};

export const isZero = (value: number): boolean => {
  return value === 0;
};

export const isEven = (value: number): boolean => {
  return isInteger(value) && value % 2 === 0;
};

export const isOdd = (value: number): boolean => {
  return isInteger(value) && value % 2 !== 0;
};

export const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max);
};

export const round = (value: number, decimals: number = 0): number => {
  const factor = Math.pow(10, decimals);
  return Math.round(value * factor) / factor;
};

export const floor = (value: number, decimals: number = 0): number => {
  const factor = Math.pow(10, decimals);
  return Math.floor(value * factor) / factor;
};

export const ceil = (value: number, decimals: number = 0): number => {
  const factor = Math.pow(10, decimals);
  return Math.ceil(value * factor) / factor;
};

export const toFixed = (value: number, decimals: number = 2): string => {
  return value.toFixed(decimals);
};

export const toPrecision = (value: number, precision: number = 2): string => {
  return value.toPrecision(precision);
};

export const toExponential = (value: number, fractionDigits?: number): string => {
  return value.toExponential(fractionDigits);
};

export const formatNumber = (
  value: number,
  locale: 'ar' | 'en' = 'ar',
  options?: Intl.NumberFormatOptions
): string => {
  const localeCode = locale === 'ar' ? 'ar-SY' : 'en-US';
  return new Intl.NumberFormat(localeCode, options).format(value);
};

export const formatCurrency = (
  value: number,
  currency: 'USD' | 'SYP' = 'USD',
  locale: 'ar' | 'en' = 'ar'
): string => {
  const localeCode = locale === 'ar' ? 'ar-SY' : 'en-US';
  return new Intl.NumberFormat(localeCode, {
    style: 'currency',
    currency,
    minimumFractionDigits: currency === 'SYP' ? 0 : 2,
  }).format(value);
};

export const formatPercentage = (
  value: number,
  decimals: number = 1,
  locale: 'ar' | 'en' = 'ar'
): string => {
  const localeCode = locale === 'ar' ? 'ar-SY' : 'en-US';
  return new Intl.NumberFormat(localeCode, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};

export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
};

export const parseNumber = (value: string | number): number | null => {
  if (typeof value === 'number') return isNumber(value) ? value : null;
  
  const parsed = parseFloat(value);
  return isNumber(parsed) ? parsed : null;
};

export const parseInt = (value: string | number, radix: number = 10): number | null => {
  if (typeof value === 'number') return isInteger(value) ? value : null;
  
  const parsed = Number.parseInt(value, radix);
  return isInteger(parsed) ? parsed : null;
};

export const random = (min: number = 0, max: number = 1): number => {
  return Math.random() * (max - min) + min;
};

export const randomInt = (min: number, max: number): number => {
  return Math.floor(random(min, max + 1));
};

export const randomFloat = (min: number, max: number, decimals: number = 2): number => {
  return round(random(min, max), decimals);
};

export const sum = (numbers: number[]): number => {
  return numbers.reduce((total, num) => total + num, 0);
};

export const average = (numbers: number[]): number => {
  return numbers.length === 0 ? 0 : sum(numbers) / numbers.length;
};

export const median = (numbers: number[]): number => {
  if (numbers.length === 0) return 0;
  
  const sorted = [...numbers].sort((a, b) => a - b);
  const mid = Math.floor(sorted.length / 2);
  
  return sorted.length % 2 === 0
    ? (sorted[mid - 1] + sorted[mid]) / 2
    : sorted[mid];
};

export const mode = (numbers: number[]): number => {
  if (numbers.length === 0) return 0;
  
  const frequency: Record<number, number> = {};
  let maxFreq = 0;
  let mode = numbers[0];
  
  numbers.forEach(num => {
    frequency[num] = (frequency[num] || 0) + 1;
    if (frequency[num] > maxFreq) {
      maxFreq = frequency[num];
      mode = num;
    }
  });
  
  return mode;
};

export const min = (numbers: number[]): number => {
  return Math.min(...numbers);
};

export const max = (numbers: number[]): number => {
  return Math.max(...numbers);
};

export const range = (numbers: number[]): number => {
  return max(numbers) - min(numbers);
};

export const variance = (numbers: number[]): number => {
  if (numbers.length === 0) return 0;
  
  const avg = average(numbers);
  const squaredDiffs = numbers.map(num => Math.pow(num - avg, 2));
  return average(squaredDiffs);
};

export const standardDeviation = (numbers: number[]): number => {
  return Math.sqrt(variance(numbers));
};

export const percentile = (numbers: number[], p: number): number => {
  if (numbers.length === 0) return 0;
  
  const sorted = [...numbers].sort((a, b) => a - b);
  const index = (p / 100) * (sorted.length - 1);
  
  if (Number.isInteger(index)) {
    return sorted[index];
  }
  
  const lower = Math.floor(index);
  const upper = Math.ceil(index);
  const weight = index - lower;
  
  return sorted[lower] * (1 - weight) + sorted[upper] * weight;
};

export const quartiles = (numbers: number[]): [number, number, number] => {
  return [
    percentile(numbers, 25),
    percentile(numbers, 50),
    percentile(numbers, 75),
  ];
};

export const interquartileRange = (numbers: number[]): number => {
  const [q1, , q3] = quartiles(numbers);
  return q3 - q1;
};

export const outliers = (numbers: number[]): number[] => {
  const [q1, , q3] = quartiles(numbers);
  const iqr = interquartileRange(numbers);
  const lowerBound = q1 - 1.5 * iqr;
  const upperBound = q3 + 1.5 * iqr;
  
  return numbers.filter(num => num < lowerBound || num > upperBound);
};

export const normalize = (numbers: number[]): number[] => {
  const minVal = min(numbers);
  const maxVal = max(numbers);
  const range = maxVal - minVal;
  
  if (range === 0) return numbers.map(() => 0);
  
  return numbers.map(num => (num - minVal) / range);
};

export const standardize = (numbers: number[]): number[] => {
  const avg = average(numbers);
  const stdDev = standardDeviation(numbers);
  
  if (stdDev === 0) return numbers.map(() => 0);
  
  return numbers.map(num => (num - avg) / stdDev);
};

export const factorial = (n: number): number => {
  if (!isInteger(n) || n < 0) return NaN;
  if (n === 0 || n === 1) return 1;
  
  let result = 1;
  for (let i = 2; i <= n; i++) {
    result *= i;
  }
  return result;
};

export const fibonacci = (n: number): number => {
  if (!isInteger(n) || n < 0) return NaN;
  if (n === 0) return 0;
  if (n === 1) return 1;
  
  let a = 0, b = 1;
  for (let i = 2; i <= n; i++) {
    [a, b] = [b, a + b];
  }
  return b;
};

export const isPrime = (n: number): boolean => {
  if (!isInteger(n) || n < 2) return false;
  if (n === 2) return true;
  if (n % 2 === 0) return false;
  
  for (let i = 3; i <= Math.sqrt(n); i += 2) {
    if (n % i === 0) return false;
  }
  return true;
};

export const gcd = (a: number, b: number): number => {
  if (!isInteger(a) || !isInteger(b)) return NaN;
  
  a = Math.abs(a);
  b = Math.abs(b);
  
  while (b !== 0) {
    [a, b] = [b, a % b];
  }
  return a;
};

export const lcm = (a: number, b: number): number => {
  if (!isInteger(a) || !isInteger(b)) return NaN;
  
  return Math.abs(a * b) / gcd(a, b);
};

export const toRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

export const toDegrees = (radians: number): number => {
  return radians * (180 / Math.PI);
};
