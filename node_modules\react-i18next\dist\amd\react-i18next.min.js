define(["exports","react"],(function(e,n){"use strict";function t(){return t=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},t.apply(this,arguments)}function s(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var i=s({area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),r=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function o(e){var n={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},t=e.match(/<\/?([^\s]+?)[/\s>]/);if(t&&(n.name=t[1],(i[t[1]]||"/"===e.charAt(e.length-2))&&(n.voidElement=!0),n.name.startsWith("!--"))){var s=e.indexOf("--\x3e");return{type:"comment",comment:-1!==s?e.slice(4,s):""}}for(var o=new RegExp(r),a=null;null!==(a=o.exec(e));)if(a[0].trim())if(a[1]){var c=a[1].trim(),l=[c,""];c.indexOf("=")>-1&&(l=c.split("=")),n.attrs[l[0]]=l[1],o.lastIndex--}else a[2]&&(n.attrs[a[2]]=a[3].trim().substring(1,a[3].length-1));return n}var a=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,c=/^\s*$/,l=Object.create(null);function u(e,n){switch(n.type){case"text":return e+n.content;case"tag":return e+="<"+n.name+(n.attrs?function(e){var n=[];for(var t in e)n.push(t+'="'+e[t]+'"');return n.length?" "+n.join(" "):""}(n.attrs):"")+(n.voidElement?"/>":">"),n.voidElement?e:e+n.children.reduce(u,"")+"</"+n.name+">";case"comment":return e+"\x3c!--"+n.comment+"--\x3e"}}var p={parse:function(e,n){n||(n={}),n.components||(n.components=l);var t,s=[],i=[],r=-1,u=!1;if(0!==e.indexOf("<")){var p=e.indexOf("<");s.push({type:"text",content:-1===p?e:e.substring(0,p)})}return e.replace(a,(function(a,l){if(u){if(a!=="</"+t.name+">")return;u=!1}var p,d="/"!==a.charAt(1),f=a.startsWith("\x3c!--"),g=l+a.length,h=e.charAt(g);if(f){var m=o(a);return r<0?(s.push(m),s):((p=i[r]).children.push(m),s)}if(d&&(r++,"tag"===(t=o(a)).type&&n.components[t.name]&&(t.type="component",u=!0),t.voidElement||u||!h||"<"===h||t.children.push({type:"text",content:e.slice(g,e.indexOf("<",g))}),0===r&&s.push(t),(p=i[r-1])&&p.children.push(t),i[r]=t),(!d||t.voidElement)&&(r>-1&&(t.voidElement||t.name===a.slice(2,-1))&&(r--,t=-1===r?s:i[r]),!u&&"<"!==h&&h)){p=-1===r?s:i[r].children;var y=e.indexOf("<",g),b=e.slice(g,-1===y?void 0:y);c.test(b)&&(b=" "),(y>-1&&r+p.length>=0||" "!==b)&&p.push({type:"text",content:b})}})),s},stringify:function(e){return e.reduce((function(e,n){return e+u("",n)}),"")}};function d(){if(console&&console.warn){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];"string"==typeof n[0]&&(n[0]=`react-i18next:: ${n[0]}`),console.warn(...n)}}const f={};function g(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];"string"==typeof n[0]&&f[n[0]]||("string"==typeof n[0]&&(f[n[0]]=new Date),d(...n))}const h=(e,n)=>()=>{if(e.isInitialized)n();else{const t=()=>{setTimeout((()=>{e.off("initialized",t)}),0),n()};e.on("initialized",t)}};function m(e,n,t){e.loadNamespaces(n,h(e,t))}function y(e,n,t,s){"string"==typeof t&&(t=[t]),t.forEach((n=>{e.options.ns.indexOf(n)<0&&e.options.ns.push(n)})),e.loadLanguages(n,h(e,s))}function b(e){return e.displayName||e.name||("string"==typeof e&&e.length>0?e:"Unknown")}const v=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,x={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},O=e=>x[e];let E,N={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(v,O)};function $(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};N={...N,...e}}function w(){return N}function I(e){E=e}function k(){return E}function j(e,n){if(!e)return!1;const t=e.props?e.props.children:e.children;return n?t.length>0:!!t}function S(e){if(!e)return[];const n=e.props?e.props.children:e.children;return e.props&&e.props.i18nIsDynamicList?C(n):n}function C(e){return Array.isArray(e)?e:[e]}function R(e,t){if(!e)return"";let s="";const i=C(e),r=t.transSupportBasicHtmlNodes&&t.transKeepBasicHtmlNodesFor?t.transKeepBasicHtmlNodesFor:[];return i.forEach(((e,i)=>{if("string"==typeof e)s+=`${e}`;else if(n.isValidElement(e)){const n=Object.keys(e.props).length,o=r.indexOf(e.type)>-1,a=e.props.children;if(!a&&o&&0===n)s+=`<${e.type}/>`;else if(a||o&&0===n)if(e.props.i18nIsDynamicList)s+=`<${i}></${i}>`;else if(o&&1===n&&"string"==typeof a)s+=`<${e.type}>${a}</${e.type}>`;else{const e=R(a,t);s+=`<${i}>${e}</${i}>`}else s+=`<${i}></${i}>`}else if(null===e)d("Trans: the passed in value is invalid - seems you passed in a null child.");else if("object"==typeof e){const{format:n,...t}=e,i=Object.keys(t);if(1===i.length){const e=n?`${i[0]}, ${n}`:i[0];s+=`{{${e}}}`}else d("react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.",e)}else d("Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.",e)})),s}function L(e,s,i,r,o,a){if(""===s)return[];const c=r.transKeepBasicHtmlNodesFor||[],l=s&&new RegExp(c.map((e=>`<${e}`)).join("|")).test(s);if(!e&&!l&&!a)return[s];const u={};!function e(t){C(t).forEach((t=>{"string"!=typeof t&&(j(t)?e(S(t)):"object"!=typeof t||n.isValidElement(t)||Object.assign(u,t))}))}(e);const d=p.parse(`<0>${s}</0>`),f={...u,...o};function g(e,t,s){const i=S(e),r=m(i,t.children,s);return function(e){return"[object Array]"===Object.prototype.toString.call(e)&&e.every((e=>n.isValidElement(e)))}(i)&&0===r.length||e.props&&e.props.i18nIsDynamicList?i:r}function h(e,s,i,r,o){e.dummy?(e.children=s,i.push(n.cloneElement(e,{key:r},o?void 0:s))):i.push(...n.Children.map([e],(e=>{const i={...e.props};return delete i.i18nIsDynamicList,n.createElement(e.type,t({},i,{key:r,ref:e.ref},o?{}:{children:s}))})))}function m(t,s,o){const u=C(t);return C(s).reduce(((t,s,p)=>{const d=s.children&&s.children[0]&&s.children[0].content&&i.services.interpolator.interpolate(s.children[0].content,f,i.language);if("tag"===s.type){let a=u[parseInt(s.name,10)];1!==o.length||a||(a=o[0][s.name]),a||(a={});const y=0!==Object.keys(s.attrs).length?function(e,n){const t={...n};return t.props=Object.assign(e.props,n.props),t}({props:s.attrs},a):a,b=n.isValidElement(y),v=b&&j(s,!0)&&!s.voidElement,x=l&&"object"==typeof y&&y.dummy&&!b,O="object"==typeof e&&null!==e&&Object.hasOwnProperty.call(e,s.name);if("string"==typeof y){const e=i.services.interpolator.interpolate(y,f,i.language);t.push(e)}else if(j(y)||v){h(y,g(y,s,o),t,p)}else if(x){h(y,m(u,s.children,o),t,p)}else if(Number.isNaN(parseFloat(s.name)))if(O){h(y,g(y,s,o),t,p,s.voidElement)}else if(r.transSupportBasicHtmlNodes&&c.indexOf(s.name)>-1)if(s.voidElement)t.push(n.createElement(s.name,{key:`${s.name}-${p}`}));else{const e=m(u,s.children,o);t.push(n.createElement(s.name,{key:`${s.name}-${p}`},e))}else if(s.voidElement)t.push(`<${s.name} />`);else{const e=m(u,s.children,o);t.push(`<${s.name}>${e}</${s.name}>`)}else if("object"!=typeof y||b)h(y,d,t,p,1!==s.children.length||!d);else{const e=s.children[0]?d:null;e&&t.push(e)}}else if("text"===s.type){const e=r.transWrapTextNodes,o=a?r.unescape(i.services.interpolator.interpolate(s.content,f,i.language)):i.services.interpolator.interpolate(s.content,f,i.language);e?t.push(n.createElement(e,{key:`${s.name}-${p}`},o)):t.push(o)}return t}),[])}return S(m([{dummy:!0,children:e||[]}],d,C(e||[]))[0])}function T(e){let{children:t,count:s,parent:i,i18nKey:r,context:o,tOptions:a={},values:c,defaults:l,components:u,ns:p,i18n:d,t:f,shouldUnescape:h,...m}=e;const y=d||k();if(!y)return g("You will need to pass in an i18next instance by using i18nextReactModule"),t;const b=f||y.t.bind(y)||(e=>e);o&&(a.context=o);const v={...w(),...y.options&&y.options.react};let x=p||b.ns||y.options&&y.options.defaultNS;x="string"==typeof x?[x]:x||["translation"];const O=R(t,v),E=l||O||v.transEmptyNodeValue||r,{hashTransKey:N}=v,$=r||(N?N(O||E):O||E);y.options&&y.options.interpolation&&y.options.interpolation.defaultVariables&&(c=c&&Object.keys(c).length>0?{...c,...y.options.interpolation.defaultVariables}:{...y.options.interpolation.defaultVariables});const I=c?a.interpolation:{interpolation:{...a.interpolation,prefix:"#$?",suffix:"?$#"}},j={...a,count:s,...c,...I,defaultValue:E,ns:x},S=$?b($,j):E;u&&Object.keys(u).forEach((e=>{const t=u[e];"function"==typeof t.type||!t.props||!t.props.children||S.indexOf(`${e}/>`)<0&&S.indexOf(`${e} />`)<0||(u[e]=n.createElement((function(){return n.createElement(n.Fragment,null,t)}),null))}));const C=L(u||t,S,y,v,j,h),T=void 0!==i?i:v.defaultTransParent;return T?n.createElement(T,m,C):C}const P={type:"3rdParty",init(e){$(e.options.react),I(e)}},V=n.createContext();class z{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}function A(e){return n=>new Promise((t=>{const s=B();e.getInitialProps?e.getInitialProps(n).then((e=>{t({...e,...s})})):t(s)}))}function B(){const e=k(),n=e.reportNamespaces?e.reportNamespaces.getUsedNamespaces():[],t={},s={};return e.languages.forEach((t=>{s[t]={},n.forEach((n=>{s[t][n]=e.getResourceBundle(t,n)||{}}))})),t.initialI18nStore=s,t.initialLanguage=e.language,t}const F=(e,t)=>{const s=n.useRef();return n.useEffect((()=>{s.current=t?s.current:e}),[e,t]),s.current};function U(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:s}=t,{i18n:i,defaultNS:r}=n.useContext(V)||{},o=s||i||k();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new z),!o){g("You will need to pass in an i18next instance by using initReactI18next");const e=(e,n)=>"string"==typeof n?n:n&&"object"==typeof n&&"string"==typeof n.defaultValue?n.defaultValue:Array.isArray(e)?e[e.length-1]:e,n=[e,{},!1];return n.t=e,n.i18n={},n.ready=!1,n}o.options.react&&void 0!==o.options.react.wait&&g("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...w(),...o.options.react,...t},{useSuspense:c,keyPrefix:l}=a;let u=e||r||o.options&&o.options.defaultNS;u="string"==typeof u?[u]:u||["translation"],o.reportNamespaces.addUsedNamespaces&&o.reportNamespaces.addUsedNamespaces(u);const p=(o.isInitialized||o.initializedStoreOnce)&&u.every((e=>function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.languages&&n.languages.length?void 0!==n.options.ignoreJSONStructure?n.hasLoadedNamespace(e,{lng:t.lng,precheck:(n,s)=>{if(t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!s(n.isLanguageChangingTo,e))return!1}}):function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=n.languages[0],i=!!n.options&&n.options.fallbackLng,r=n.languages[n.languages.length-1];if("cimode"===s.toLowerCase())return!0;const o=(e,t)=>{const s=n.services.backendConnector.state[`${e}|${t}`];return-1===s||2===s};return!(t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!o(n.isLanguageChangingTo,e)||!n.hasResourceBundle(s,e)&&n.services.backendConnector.backend&&(!n.options.resources||n.options.partialBundledLanguages)&&(!o(s,e)||i&&!o(r,e)))}(e,n,t):(g("i18n.languages were undefined or empty",n.languages),!0)}(e,o,a)));function d(){return o.getFixedT(t.lng||null,"fallback"===a.nsMode?u:u[0],l)}const[f,h]=n.useState(d);let b=u.join();t.lng&&(b=`${t.lng}${b}`);const v=F(b),x=n.useRef(!0);n.useEffect((()=>{const{bindI18n:e,bindI18nStore:n}=a;function s(){x.current&&h(d)}return x.current=!0,p||c||(t.lng?y(o,t.lng,u,(()=>{x.current&&h(d)})):m(o,u,(()=>{x.current&&h(d)}))),p&&v&&v!==b&&x.current&&h(d),e&&o&&o.on(e,s),n&&o&&o.store.on(n,s),()=>{x.current=!1,e&&o&&e.split(" ").forEach((e=>o.off(e,s))),n&&o&&n.split(" ").forEach((e=>o.store.off(e,s)))}}),[o,b]);const O=n.useRef(!0);n.useEffect((()=>{x.current&&!O.current&&h(d),O.current=!1}),[o,l]);const E=[f,o,p];if(E.t=f,E.i18n=o,E.ready=p,p)return E;if(!p&&!c)return E;throw new Promise((e=>{t.lng?y(o,t.lng,u,(()=>e())):m(o,u,(()=>e()))}))}function K(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{i18n:i}=s,{i18n:r}=n.useContext(V)||{},o=i||r||k();o.options&&o.options.isClone||(e&&!o.initializedStoreOnce&&(o.services.resourceStore.data=e,o.options.ns=Object.values(e).reduce(((e,n)=>(Object.keys(n).forEach((n=>{e.indexOf(n)<0&&e.push(n)})),e)),o.options.ns),o.initializedStoreOnce=!0,o.isInitialized=!0),t&&!o.initializedLanguageOnce&&(o.changeLanguage(t),o.initializedLanguageOnce=!0))}e.I18nContext=V,e.I18nextProvider=function(e){let{i18n:t,defaultNS:s,children:i}=e;const r=n.useMemo((()=>({i18n:t,defaultNS:s})),[t,s]);return n.createElement(V.Provider,{value:r},i)},e.Trans=function(e){let{children:t,count:s,parent:i,i18nKey:r,context:o,tOptions:a={},values:c,defaults:l,components:u,ns:p,i18n:d,t:f,shouldUnescape:g,...h}=e;const{i18n:m,defaultNS:y}=n.useContext(V)||{},b=d||m||k(),v=f||b&&b.t.bind(b);return T({children:t,count:s,parent:i,i18nKey:r,context:o,tOptions:a,values:c,defaults:l,components:u,ns:p||v&&v.ns||y||b&&b.options&&b.options.defaultNS,i18n:b,t:f,shouldUnescape:g,...h})},e.TransWithoutContext=T,e.Translation=function(e){const{ns:n,children:t,...s}=e,[i,r,o]=U(n,s);return t(i,{i18n:r,lng:r.language},o)},e.composeInitialProps=A,e.date=()=>"",e.getDefaults=w,e.getI18n=k,e.getInitialProps=B,e.initReactI18next=P,e.number=()=>"",e.plural=()=>"",e.select=()=>"",e.selectOrdinal=()=>"",e.setDefaults=$,e.setI18n=I,e.time=()=>"",e.useSSR=K,e.useTranslation=U,e.withSSR=function(){return function(e){function t(t){let{initialI18nStore:s,initialLanguage:i,...r}=t;return K(s,i),n.createElement(e,{...r})}return t.getInitialProps=A(e),t.displayName=`withI18nextSSR(${b(e)})`,t.WrappedComponent=e,t}},e.withTranslation=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(s){function i(i){let{forwardedRef:r,...o}=i;const[a,c,l]=U(e,{...o,keyPrefix:t.keyPrefix}),u={...o,t:a,i18n:c,tReady:l};return t.withRef&&r?u.ref=r:!t.withRef&&r&&(u.forwardedRef=r),n.createElement(s,u)}i.displayName=`withI18nextTranslation(${b(s)})`,i.WrappedComponent=s;return t.withRef?n.forwardRef(((e,t)=>n.createElement(i,Object.assign({},e,{forwardedRef:t})))):i}}}));
