// Array utilities

export const chunk = <T>(array: T[], size: number): T[][] => {
  if (size <= 0) return [];
  
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

export const flatten = <T>(array: (T | T[])[]): T[] => {
  return array.reduce<T[]>((acc, val) => 
    Array.isArray(val) ? acc.concat(flatten(val)) : acc.concat(val), []
  );
};

export const unique = <T>(array: T[]): T[] => {
  return [...new Set(array)];
};

export const uniqueBy = <T, K>(array: T[], keyFn: (item: T) => K): T[] => {
  const seen = new Set<K>();
  return array.filter(item => {
    const key = keyFn(item);
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

export const groupBy = <T, K extends keyof T>(
  array: T[],
  key: K
): Record<string, T[]> => {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
};

export const groupByFn = <T, K>(
  array: T[],
  keyFn: (item: T) => K
): Map<K, T[]> => {
  const groups = new Map<K, T[]>();
  
  array.forEach(item => {
    const key = keyFn(item);
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key)!.push(item);
  });
  
  return groups;
};

export const sortBy = <T>(
  array: T[],
  key: keyof T,
  direction: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...array].sort((a, b) => {
    const aVal = a[key];
    const bVal = b[key];
    
    if (aVal < bVal) return direction === 'asc' ? -1 : 1;
    if (aVal > bVal) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

export const sortByFn = <T>(
  array: T[],
  compareFn: (a: T, b: T) => number
): T[] => {
  return [...array].sort(compareFn);
};

export const shuffle = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export const sample = <T>(array: T[]): T | undefined => {
  return array[Math.floor(Math.random() * array.length)];
};

export const sampleSize = <T>(array: T[], size: number): T[] => {
  const shuffled = shuffle(array);
  return shuffled.slice(0, Math.min(size, array.length));
};

export const intersection = <T>(...arrays: T[][]): T[] => {
  if (arrays.length === 0) return [];
  if (arrays.length === 1) return [...arrays[0]];
  
  return arrays.reduce((acc, curr) => 
    acc.filter(item => curr.includes(item))
  );
};

export const union = <T>(...arrays: T[][]): T[] => {
  return unique(arrays.flat());
};

export const difference = <T>(array: T[], ...others: T[][]): T[] => {
  const otherItems = new Set(others.flat());
  return array.filter(item => !otherItems.has(item));
};

export const symmetricDifference = <T>(array1: T[], array2: T[]): T[] => {
  return [
    ...difference(array1, array2),
    ...difference(array2, array1)
  ];
};

export const partition = <T>(
  array: T[],
  predicate: (item: T) => boolean
): [T[], T[]] => {
  const truthy: T[] = [];
  const falsy: T[] = [];
  
  array.forEach(item => {
    if (predicate(item)) {
      truthy.push(item);
    } else {
      falsy.push(item);
    }
  });
  
  return [truthy, falsy];
};

export const compact = <T>(array: (T | null | undefined | false | 0 | '')[]): T[] => {
  return array.filter(Boolean) as T[];
};

export const zip = <T, U>(array1: T[], array2: U[]): [T, U][] => {
  const length = Math.min(array1.length, array2.length);
  const result: [T, U][] = [];
  
  for (let i = 0; i < length; i++) {
    result.push([array1[i], array2[i]]);
  }
  
  return result;
};

export const unzip = <T, U>(array: [T, U][]): [T[], U[]] => {
  const first: T[] = [];
  const second: U[] = [];
  
  array.forEach(([a, b]) => {
    first.push(a);
    second.push(b);
  });
  
  return [first, second];
};

export const transpose = <T>(matrix: T[][]): T[][] => {
  if (matrix.length === 0) return [];
  
  const rows = matrix.length;
  const cols = matrix[0].length;
  const result: T[][] = [];
  
  for (let j = 0; j < cols; j++) {
    result[j] = [];
    for (let i = 0; i < rows; i++) {
      result[j][i] = matrix[i][j];
    }
  }
  
  return result;
};

export const range = (start: number, end?: number, step: number = 1): number[] => {
  if (end === undefined) {
    end = start;
    start = 0;
  }
  
  const result: number[] = [];
  
  if (step > 0) {
    for (let i = start; i < end; i += step) {
      result.push(i);
    }
  } else if (step < 0) {
    for (let i = start; i > end; i += step) {
      result.push(i);
    }
  }
  
  return result;
};

export const fill = <T>(length: number, value: T): T[] => {
  return Array(length).fill(value);
};

export const times = <T>(length: number, iteratee: (index: number) => T): T[] => {
  return Array.from({ length }, (_, index) => iteratee(index));
};

export const first = <T>(array: T[]): T | undefined => {
  return array[0];
};

export const last = <T>(array: T[]): T | undefined => {
  return array[array.length - 1];
};

export const nth = <T>(array: T[], index: number): T | undefined => {
  return index >= 0 ? array[index] : array[array.length + index];
};

export const take = <T>(array: T[], count: number): T[] => {
  return array.slice(0, Math.max(0, count));
};

export const takeRight = <T>(array: T[], count: number): T[] => {
  return array.slice(-Math.max(0, count));
};

export const drop = <T>(array: T[], count: number): T[] => {
  return array.slice(Math.max(0, count));
};

export const dropRight = <T>(array: T[], count: number): T[] => {
  return array.slice(0, -Math.max(0, count));
};

export const takeWhile = <T>(
  array: T[],
  predicate: (item: T) => boolean
): T[] => {
  const result: T[] = [];
  
  for (const item of array) {
    if (!predicate(item)) break;
    result.push(item);
  }
  
  return result;
};

export const dropWhile = <T>(
  array: T[],
  predicate: (item: T) => boolean
): T[] => {
  let index = 0;
  
  while (index < array.length && predicate(array[index])) {
    index++;
  }
  
  return array.slice(index);
};

export const findIndex = <T>(
  array: T[],
  predicate: (item: T) => boolean
): number => {
  return array.findIndex(predicate);
};

export const findLastIndex = <T>(
  array: T[],
  predicate: (item: T) => boolean
): number => {
  for (let i = array.length - 1; i >= 0; i--) {
    if (predicate(array[i])) {
      return i;
    }
  }
  return -1;
};

export const includes = <T>(array: T[], value: T): boolean => {
  return array.includes(value);
};

export const isEmpty = <T>(array: T[]): boolean => {
  return array.length === 0;
};

export const isEqual = <T>(array1: T[], array2: T[]): boolean => {
  if (array1.length !== array2.length) return false;
  
  return array1.every((item, index) => item === array2[index]);
};

export const sum = (array: number[]): number => {
  return array.reduce((total, num) => total + num, 0);
};

export const product = (array: number[]): number => {
  return array.reduce((total, num) => total * num, 1);
};

export const min = (array: number[]): number => {
  return Math.min(...array);
};

export const max = (array: number[]): number => {
  return Math.max(...array);
};

export const minBy = <T>(
  array: T[],
  iteratee: (item: T) => number
): T | undefined => {
  if (array.length === 0) return undefined;
  
  return array.reduce((min, current) => 
    iteratee(current) < iteratee(min) ? current : min
  );
};

export const maxBy = <T>(
  array: T[],
  iteratee: (item: T) => number
): T | undefined => {
  if (array.length === 0) return undefined;
  
  return array.reduce((max, current) => 
    iteratee(current) > iteratee(max) ? current : max
  );
};

export const countBy = <T, K>(
  array: T[],
  iteratee: (item: T) => K
): Map<K, number> => {
  const counts = new Map<K, number>();
  
  array.forEach(item => {
    const key = iteratee(item);
    counts.set(key, (counts.get(key) || 0) + 1);
  });
  
  return counts;
};

export const frequencies = <T>(array: T[]): Map<T, number> => {
  return countBy(array, item => item);
};
